@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.LeftMenu = "Goods";
    ViewBag.LeftChileMenu = "GoodsAdd";
    var storeId = Store.FindByUId(ManageProvider.User?.ID ?? 0)?.Id ?? 0;
    var materialList = MerchantMaterial.GetEnableList(storeId);
}
@await Html.PartialAsync("_Left")


<div class="seller_right">
    <div class="seller_items">
    </div>
    <div class="p20">
        <ul class="add-goods-step">
            <li>
                <i class="icon iconfont">&#xe600;</i>
                <h6>STEP.1</h6>
                <h2>选择商品分类</h2>
                <i class="arrow iconfont">&#xe687;</i>
            </li>
            <li>
                <i class="icon iconfont">&#xe731;</i>
                <h6>STEP.2</h6>
                <h2>填写商品详情</h2>
                <i class="arrow iconfont">&#xe687;</i>
            </li>
            <li class="current">
                <i class="icon iconfont">&#xe71d;</i>
                <h6>STEP.3</h6>
                <h2>添加商品SKU</h2>
                <i class="arrow iconfont">&#xe687;</i>
            </li>
            <li>
                <i class="icon iconfont">&#xe6a2;</i>
                <h6>STEP.4</h6>
                <h2>添加商品图片</h2>
                <i class="arrow iconfont">&#xe687;</i>
            </li>
            <li>
                <i class="icon iconfont">&#xe64d;</i>
                <h6>STEP.5</h6>
                <h2>商品发布成功</h2>
            </li>
        </ul>
        <form method="post" id="goods_sku" action="@Url.Action("AddGoodsSku")">
            <input type="hidden" name="goodsId" value="@Model.goods.Id" />
            <dl id="dl_spec">
                <dt>@T("商品SKU")：</dt>
                <dd>
                    <div>
                        <ul class="spec">
                            @{
                                List<int> showSpecIds = new();//勾选的规格
                                foreach (GoodsSpecValue item in Model.specList)
                                {
                                    <li>
                                        <label class="spec-checkbox-item">
                                            <input type="checkbox" name="speclist" value="@item.Id" onchange="handleSpecChange(this, '@item.Name')"/>
                                            <span>@item.Name</span>
                                        </label>
                                    </li>
                                }
                            }
                        </ul>
                    </div>
                </dd>
            </dl>

            <dl ds_type="spec_dl" class="spec-bg" style="overflow: visible; display: none;">
                <dt>@T("SKU配置")：</dt>
                <dd class="spec-dd">
                    <input type="hidden" name="skuCount" id="skuCount" value="0" />
                    <input type="hidden" name="specValueCount" id="specValueCount" value="0" />
                    <table border="0" cellpadding="0" cellspacing="0" class="spec_table" id="specTable">
                        <thead>
                            <tr>
                                @foreach (GoodsSpecValue item in Model.specList)
                                {
                                    <th data-spec-id="@item.Id" dstype="<EMAIL>" data-type="<EMAIL>" style="display: none;">@item.Name</th>
                                }
                                <th class="w100" data-field="materialId">@T("商品物料")</th>

                                <th class="w100">
                                    @T("市场价")
                                </th>
                                <th class="w90">
                                    @T("价格")
                                </th>
                                <th class="w90">
                                    @T("阶梯价")
                                </th>
                            </tr>
                        </thead>
                        <tbody ds_type="spec_table">
                        </tbody>
                    </table>
                    <p class="hint">点击<i class="iconfont"></i>可批量修改所在列的值。</p>
                </dd>
            </dl>
            <distributed-cache class="seller_items" style="margin:10px;">
                <p class="hint"><span style="color:red">@T("注：")</span>@T("设置SKU属性后，商品原来设置的价格和阶梯价将会失效")</p>
            </distributed-cache>
            <div class="bottom tc hr32">
                <input type="submit" class="submit" value="下一步，添加商品图片" style="margin-top: 100px;"/>
            </div>
        </form>
    </div>
</div>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script asp-location="Footer">
     // 定义存储规格值的数组
    var selectedSpecValues = {};
    function handleSpecChange(checkbox, specName) {
         var addSpecUrl = '@Url.Action("AddSpec", "SellerGoodsSpecManage", new { area = "Sellers" })';
         if (checkbox.checked) {
             $.ajax({
                 url: '/Sellers/SellerGoodsSpecManage/GetSpecValue',
                 type: 'GET',
                 data: { id: checkbox.value, goodsId: '@Model.goods.Id' },
                 success: function(result) {
                     // 创建固定位置的容器
                     var fixedContainer = $(`
                         <div class="fixed-spec-container" data-spec-id="${checkbox.value}" style="position: relative;">
                             <dt data-spec-id="${checkbox.value}">${specName}：</dt>
                             <dd data-spec-id="${checkbox.value}">
                                 <div>
                                     <ul class="spec">
                                         <li>
                                             <span class="new_add">
                                                 <a href="javascript:void(0);" onclick="openAddSpecDialog(${checkbox.value});return false;" id="add_sgcategory" class="dssc-btn">@T("新增属性")</a>
                                             </span>
                                         </li>
                                         ${result.map(item => `
                                             <li>
                                                 <label class="spec-value-checkbox">
                                                     <input type="checkbox" name="specValue_${checkbox.value}" value="${item.Id}" onclick="handleSpecValueSelect(${checkbox.value}, '${specName}', ${item.Id}, '${item.Name}', this.checked)" />
                                                     <span>${item.Name}</span>
                                                 </label>
                                             </li>
                                         `).join('')}
                                     </ul>
                                 </div>
                             </dd>
                         </div>
                     `);
                     
                     // 添加到固定位置
                     $("#dl_spec").append(fixedContainer);
                 }
             });
         } else {
             // 移除对应的规格值区域
             $(`div.fixed-spec-container[data-spec-id="${checkbox.value}"]`).remove();
             delete selectedSpecValues[checkbox.value];
             $(`th[data-type="spec_name_${checkbox.value}"]`).hide();
             
             var hasSelectedValues = Object.keys(selectedSpecValues).some(key => selectedSpecValues[key].values.length > 0);
             if (!hasSelectedValues) {
                 $('dl[ds_type="spec_dl"]').hide();
             }
         }
    }

      function handleSpecValueSelect(specId, specName, valueId, valueName, checked) {
            if (!selectedSpecValues[specId]) {
                selectedSpecValues[specId] = {
                    specId: specId,
                    specName: specName,
                    values: []
                };
            }

            if (checked) {
                // 添加规格值
                selectedSpecValues[specId].values.push({
                    id: valueId,
                    name: valueName
                });
                // 当该规格有选中的值时，显示对应的列
                $(`th[data-type="spec_name_${specId}"]`).show();
            } else {
                // 移除规格值
                selectedSpecValues[specId].values = selectedSpecValues[specId].values.filter(v => v.id !== valueId);
                // 当该规格没有选中的值时，隐藏对应的列
                if (selectedSpecValues[specId].values.length === 0) {
                    $(`th[data-type="spec_name_${specId}"]`).hide();
                }
            }

            // 如果该规格下没有选中的值，则删除该规格
            if (selectedSpecValues[specId].values.length === 0) {
                delete selectedSpecValues[specId];
            }
            var hasSelectedValues = Object.keys(selectedSpecValues).some(key => selectedSpecValues[key].values.length > 0);
            if (hasSelectedValues) {
               $('dl[ds_type="spec_dl"]').show();
            } else {
               $('dl[ds_type="spec_dl"]').hide();
            }
            // 更新规格表格
            updateSpecTable();
            console.log('当前选中的规格值：', selectedSpecValues);
       }
       // 添加新函数用于更新规格表格
    function updateSpecTable() {
        // 保存现有行的数据
        let existingData = {};
        $('tbody[ds_type="spec_table"] tr').each(function() {
            let key = [];
            $(this).find('input[type="hidden"][name*="sp_value"]').each(function() {
                key.push($(this).val());
            });
            key = key.sort().join('_');

            existingData[key] = {
                id: $(this).find('input[data_type="id"]').val(),
                marketPrice: $(this).find('input[data_type="marketprice"]').val(),
                price: $(this).find('input[data_type="price"]').val(),
                material: $(this).find('select[data_type="material"]').val()
            };
        });
        // 清空表格内容
        $('tbody[ds_type="spec_table"]').empty();

        // 获取所有选中的规格值组合
        let combinations = generateSpecCombinations();
        let specCount = Object.keys(combinations[0] || {}).length;
        // 记录规格组合的数量和规格值数量
        $('#skuCount').val(combinations.length);
        $('#specValueCount').val(specCount);
         var materialList = @Html.Raw(Json.Serialize(materialList.Select(m => new { Id = m.Id.SafeString(), Name = m.Name })));
        // 生成表格行
        combinations.forEach((combination, index) => {
            let tr = $('<tr>');
            // 生成用于匹配已有数据的key
            let valueIds = [];
            Object.keys(combination).forEach(specId => {
                valueIds.push(combination[specId].id);
            });
            let dataKey = valueIds.sort().join('_');

            // 添加隐藏的商品ID输入，如果存在则使用原有ID
            let savedData = existingData[dataKey] || {};
            // 添加隐藏的商品ID输入
            tr.append(`<input type="hidden" name="spec[${index}][goods_id]" ds_type="${index}|id" value="">`);


            // 添加规格值单元格
            Object.keys(combination).forEach((specId, specIndex) => {
                tr.append(`
                    <td>
                        <input type="hidden" name="spec[${index}][sp_value][${specIndex}]" value="${combination[specId].id}">
                        ${combination[specId].name}
                    </td>
                `);
            });

            // 添加市场价、销售价和物料编号单元格
            tr.append(`
                <td>
                    <select name="spec[${index}][material]" data_type="material"  class="layui-select w200 material-select" lay-search lay-filter="goodsSelect" required>
                        <option value="">@T("请选择物料")</option>
                        ${materialList.map(item => `
                            <option value="${item.Id}" data-price="${item.Price}" data-marketprice="${item.MarketPrice}" ${String(savedData.material) === String(item.Id) ? 'selected="selected"' : ''}>${item.Name}</option>
                        `).join('')}
                    </select>
               </td>
                <td><input class="text price" type="text" name="spec[${index}][marketprice]" data_type="marketprice" ds_type="${index}|marketprice" value="${savedData.marketPrice || '@Model.goods.GoodsMarketPrice'}" required oninput="restrictToNumber(this, true)"></td>
                <td><input class="text price" type="text" name="spec[${index}][price]" data_type="price" ds_type="${index}|price" value="${savedData.price || '0.00'}" required oninput="restrictToNumber(this, true)"></td>
                <td>
                    <a href="javascript:void(0);" onclick="OpenTieredPriceView('${savedData.id||index}')" id="add_sgcategory" class="dssc-btn">@T("设置阶梯价")</a>
                    <input type="hidden" name="tiered[${index}][data]" data_type="tieredData" value="${savedData.tieredData || ''}">
                </td>
            `);
            $('tbody[ds_type="spec_table"]').append(tr);
             // 在添加完行后重新初始化select2
            $('select').select2({
                placeholder: "@T("请选择物料")", 
                allowClear: true
            });
        });
    }

    // 生成规格值组合
    function generateSpecCombinations() {
        let specs = Object.values(selectedSpecValues).filter(spec => spec.values.length > 0);
        if (specs.length === 0) return [];

        let combinations = [{}];

        specs.forEach(spec => {
            const newCombinations = [];
            combinations.forEach(combination => {
                spec.values.forEach(value => {
                    const newCombination = {...combination};
                    newCombination[spec.specId] = {
                        name: value.name,
                        id: value.id
                    };
                    newCombinations.push(newCombination);
                });
            });
            combinations = newCombinations;
        });

        return combinations;
    }
    function getSpecTableData() {
        let specData = [];
        $('#specTable tbody tr').each(function() {
            let row = $(this);
            let rowData = {
                specValues: {},
                marketPrice: row.find('input[data_type="marketprice"]').val(),
                price: row.find('input[data_type="price"]').val(),
                materialId: row.find('select').val()
            };

            // 获取所有显示的规格列
            $('#specTable thead th').each(function() {
                let th = $(this);
                if (th.is(':visible')) {  // 只处理显示的规格列
                    let specId = th.data('spec-id');
                    let columnIndex = th.index();
                    // 获取对应的单元格内容
                    let tdValue = row.find('td').eq(columnIndex);
                    let hiddenInput = tdValue.find('input[type="hidden"]');

                    rowData.specValues[specId] = {
                        name: hiddenInput.val(),
                        // 从selectedSpecValues中获取对应的id
                        id: selectedSpecValues[specId].values.find(v => v.name === hiddenInput.val())?.id
                    };
                }
            });

            specData.push(rowData);
        });
        return specData;
    }
</script>
<script>
    // 初始化layui模块
    layui.use(['layer'], function(){
        var layer = layui.layer;
        
        // 全局定义打开添加规格页面的方法
        window.openAddSpecDialog = function(specId) {
            var addSpecUrl = '@Url.Action("openAddSpecDialog", "SellerGoodsSpecManage", new { area = "Sellers" })' + '?id=' + specId+"&goodsId="+'@Model.goods.Id';
            
            layer.open({
                type: 2,
                title: '@T("添加规格")',
                area: ['800px', '600px'],
                fixed: false,
                maxmin: true,
                content: addSpecUrl,
                success: function(layero, index){
                    // 弹窗加载完成后的回调
                },
                end: function(){
                    $.ajax({
                        url: '/Sellers/SellerGoodsSpecManage/GetSpecValue',
                        type: 'GET',
                        data: { id: specId, goodsId: '@Model.goods.Id' },
                        success: function(result) {
                            // 直接重新加载属性值
                            var specContainer = $(`div.fixed-spec-container[data-spec-id="${specId}"]`);
                            if (specContainer.length) {
                                // 更新规格值列表
                                var specHtml = `
                                    <ul class="spec">
                                        <li>
                                            <span class="new_add">
                                                <a href="javascript:void(0);" onclick="openAddSpecDialog(${specId});return false;" id="add_sgcategory" class="dssc-btn">@T("新增属性")</a>
                                            </span>
                                        </li>
                                        ${result.map(item => `
                                            <li>
                                                <label class="spec-value-checkbox">
                                                    <input type="checkbox" name="specValue_${specId}" value="${item.Id}" onclick="handleSpecValueSelect(${specId}, '', ${item.Id}, '${item.Name}', this.checked)" />
                                                    <span>${item.Name}</span>
                                                </label>
                                            </li>
                                        `).join('')}
                                    </ul>
                                `;
                                
                                specContainer.find('ul.spec').replaceWith(specHtml);
                                
                                // 恢复之前选中的规格值
                                if (selectedSpecValues[specId] && selectedSpecValues[specId].values.length > 0) {
                                    selectedSpecValues[specId].values.forEach(value => {
                                        $(`input[name="specValue_${specId}"][value="${value.id}"]`).prop('checked', true);
                                    });
                                }
                            }
                        }
                    });
                }
            });
        };

        //打开阶梯价格设置窗口
        window.OpenTieredPriceView = function(skuId) {
             // 获取当前按钮所在的行
            var $btn = event.target || event.srcElement;
            var $tr = $($btn).closest('tr');
            // 获取价格
            var price = $tr.find('input[data_type="price"]').val();
            if (!price||price==0) {
                layer.msg('@T("请先填写价格")');
                return;
            }

            // 获取选中的物料编号
            var materialId = $tr.find('select[data_type="material"]').val();
            if (!materialId) {
                layer.msg('@T("请先选择物料")');
                return;
            }

            var tieredPriceUrl = '@Url.Action("TieredPriceView","SellerGoodsOnline", new { area = "Sellers" })' + '?skuId=' + skuId+"&goodsId="+ '@Model.goods.Id'+"&price="+price;
            layer.open({
                type: 2,
                title: '@T("设置阶梯价")',
                area: ['1000px', '600px'],
                fixed: false,
                maxmin: true,
                content: tieredPriceUrl,
                success: function(layero, index) {
                    window.tieredPriceCallback = function(data) {
                        if(data) {
                            $('input[name="tiered[' + skuId + '][data]"]').val(JSON.stringify(data)); // 将数据存储在隐藏字段中
                        }
                    };
                }
            });
        };
    });
    function restrictToNumber(input, allowDecimal) {
    if (allowDecimal) {
        // 允许小数点的处理
        input.value = input.value.replace(/[^\d.]/g, '')
            .replace(/^\./g, '')
            .replace(/\.{2,}/g, '.')
            .replace('.', '$#$')
            .replace(/\./g, '')
            .replace('$#$', '.');
    } else {
        // 只允许整数
        input.value = input.value.replace(/\D/g, '');
    }
}
$(document).ready(function() {
    $('select').select2({
        placeholder: "@T("请选择物料")",
        llowClear: true
    });
 });
</script>
<style>
.fixed-spec-container {
    margin-bottom: 15px;
    border: 1px solid #eee;
    padding: 10px;
    background: #f9f9f9;
}

</style>
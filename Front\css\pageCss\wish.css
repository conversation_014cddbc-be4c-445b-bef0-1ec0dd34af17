
.breadBox {
    border-bottom: none;
}

.main {
    margin-left: 15%;
    width: 70%;
}

.userInfoBox {
    margin-bottom: 1vw;
    /* height: 70vh; */
    display: flex;
    /* border: 1px solid ; */
}
aside {
    padding: 10px 0px;
    width: 15%;
    height: fit-content;
    border: 1px solid #E2E3E9;
    /* background-color: red; */
}

aside>div:nth-child(1) {
    font-size: 20px;
    padding-top: 10px;
    text-indent: 15%;
    font-weight: 550;
}

aside>div:not(:first-child) {
    margin-bottom: .5vw;
    padding: 7px 0vw;
    font-size: 15px;
    display: flex;
    justify-content: left;
    place-items: center;
    width: 100%;
    cursor: pointer;
}

aside>div>div:nth-child(1) {
    margin-right: .5vw;
    margin-left: 15%;
    font-size: 20px;
}

._line {
    width: 80% !important;
    height: 0px !important;
    border: none;
    border-bottom: 1px solid var(--line);
    margin: 0px 0px .7vw 10% !important;
}

aside>div:not(.bgSelect):hover {
    color: var(--blue-deep);
}

.filterBox{
    margin-bottom:1vw;
    padding: 1vw 0px;
    height: 5vw;
    border-radius: 5px;
    border: 1px solid #BABABA;
    display: flex;
}
.filterBoxContent{
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    margin-left: 5%;
    width: 70%;
    height: 100%;
    /* overflow-y: auto; */
}
.filterBoxContent>div{
    width: fit-content;
    margin-top: .5vw;
    cursor: pointer;
}
/* .filterBoxContent>div:nth-child(3n-3){
    margin-top: 0;
    color: red;
} */
/* <!-- 所有订单-表格 --> */
.content {
    width: 80%;
    display: block;
    padding: 0vw 1vw 1vw 1vw;
}

.tablesBox {
    width: 100%;
    /* height: 100vh; */
    /* border: 1px solid ; */
    overflow: auto;
    box-sizing: border-box;
}

.layui-table:not(:first-child) {
    margin-top: 2vw;
}

.tableIcon {
    font-size: 2vw;
}

th {
    border: none !important;
    font-weight: 500 !important;
    font-size: .8vw !important;
}

.goodsInfo {
    min-width: 15vw;
    display: flex;
    /* border: 1px solid red; */
}

.goodsInfo>div:nth-child(2) {
    width: 100%;
    object-fit: fill;
    text-align: center;
}

.goodsInfo>div:nth-child(3)>div {
    color: var(--text-color2);
}

.goodsInformation>div {
    margin-top: .2vw;
    display: flex;
}

.name {
    margin-left: auto;
    width: 70%;
    display: block;
    color: var(--blue-deep);
    /* border: 1px solid red; */
}

.tdTitle {
    color: var(--text-color) !important;
    font-size: 0.9vw;
    margin-bottom: 0.4vw;
}

td {
    vertical-align: top;
}

td>div:last-child {
    padding-bottom: .5vw;
}

.tableBtnBox {
    text-align: center;
}

.tableBtnBox>div:not(:first-child) {
    margin-top: .5vw;
}

.tableBtnBox>div>button {
    width: 80%;
    border-radius: 45px;
}

.fnTd>div:not(:first-child) {
    margin-top: .2vw;
}
.noData {
    width: 100%;
    height: 60vh;
    text-align: center;
}

.noDataText {
    margin-top: 1vw;
    text-align: center;
    color: rgba(0, 0, 0, 0.89);
    font-size: 1.1vw;
}

.noDataBtn {
    width: 35%;
    border-radius: 45px;
    margin-top: 1vw;
    padding: .5vw 0px;
    font-size: 1vw;
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/productDetails.css">
    <!-- 综合-script -->
    <script src="../../script/index.js" defer></script>
</head>
<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="header">
            <div class="headerItem" style="width: 10vw;">
                <img src="../../images/icons/hlk.png" style="object-fit: contain;width: 137px;">
            </div>
            <div class="headerItem" style="width: 35%;">
                <div class="searchBox flex">
                    <span class="layui-form headerForm">
                        <select class="select" name="select" id="" style="font-size: 1em;" title="分类">
                            <option value="1">商家</option>
                            <option value="2">品牌</option>
                            <option value="3">型号</option>
                            <option value="4">产品规格</option>
                        </select>
                    </span>
                    <span class="line"></span>
                    <input class="input searchInput" type="text" placeholder="请输入产品规格/型号/品牌/商家">
                    <button class="button searchButton flex" style="font-size: .9vw;">
                        <div class="iconfont icon-search" style="margin-right: 5px;font-size: 1vw;"></div>
                        搜索
                    </button>
                    <!-- 推荐关键字 -->
                    <div class="header_keyBox flex">
                        <input class="header_keyCheck" type="radio" id="option1" name="menuOptions" checked>
                        <label class="header_key textOver" for="option1">菜单1</label>

                        <input class="header_keyCheck" type="radio" id="option2" name="menuOptions">
                        <label class="header_key textOver" for="option2">菜单2</label>

                        <input class="header_keyCheck" type="radio" id="option3" name="menuOptions">
                        <label class="header_key textOver" for="option3">菜单3</label>
                    </div>
                </div>
            </div>
            <div class="headerItem" style="font-size: .9vw;min-width: 100px;width: 10%;">
                <div class="flex textOver">
                    <input type="checkbox" name="" id="goods1">
                    <label for="goods1" style="margin: -4px 5px;">库存量</label>
                </div>
                <div class="flex textOver" style="margin-left: auto;">
                    <input type="checkbox" name="" id="goods2">
                    <label for="goods2" style="margin: -4px 5px;">PHUJ</label>
                </div>
            </div>
            <div class="headerItem" style="color: var(--text-color);font-size: 1.1em;margin-left: 7%;">
                <i class="iconfont icon-weidenglu icon" style="font-size: 1.4em;margin-right: 12px;"></i>
                <span class="hoverText textOver" onclick="toRouter(this)" data-link="../login/login.html">登录</span>/
                <span class="hoverText textOver" onclick="toRouter(this)" data-link="../login/register.html">注册</span>
            </div>
            <div class="headerItem" style="color: var(--text-color);font-size: 1.1em;">
                <i class="iconfont icon-cart icon" style="font-size: 1.4em;margin-right: .2em;"></i>
                <span class="hoverText textOver">购物车</span>
            </div>
        </div>
        <!-- 头部分类 -->
        <div class="header2 textOver">
            <div class="flex productCategory" data-select="false" onclick="isSelect_productCategory(this)"
                style="margin-left: 10%;user-select: none;cursor: pointer;">
                <label for="select" class="iconfont icon-fenlei2" style="font-size: 26px;">
                </label> 产品分类 <i class="iconfont icon-xiangxia"></i>

                <div class="productCategoryBox">
                    <!-- 一级页面 -->
                    <div class="productCategory_onePage">
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> ACDC电源模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> DCDCD电源模组</span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 雷达模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> WIFI模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> BLE模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 人脸识别 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    </div>
                    <script>
                        // 监听鼠标hover事件
                        var productCategoryBox_dom = $(".productCategoryBox");
                        productCategoryBox_dom.mouseover(function (e) {
                            if (e.target.getAttribute("data-select")) {
                                $(e.target).siblings().attr("data-select", "false");
                                e.target.setAttribute("data-select", "true");
                            }
                        })
                    </script>

                    <div class="productCategory_hoverPages">

                        <div class="twoPage">
                            <div data-select="true" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> ACDC电源模组 </span>
                            </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> DCDCD电源模组</span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 雷达模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> WIFI模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> BLE模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 人脸识别 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 指纹模组 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 机壳电源 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模组 </span>
                            </div>
                        </div>

                        <div class="threePage">
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div> 

                        </div>
                        <div class="threePageMore">更多</div>
                    </div>
                    <!--  -->
                </div>
            </div>
            <span class="line2" style="height: 17px;margin-left: 10px;"></span>
            <div onclick="toRouter(this)" data-link="../index/index.html">
                <label class="header2_key">首页</label>
            </div>
            <!-- 第一版 -先干到海凌科品牌商去 -->
            <div onclick="toRouter(this)" data-link="../provider/providerDetail.html">
                <!-- <div onclick="toRouter(this)" data-link="../provider/provider.html"> -->
                <label class="header2_key">供应商</label>
            </div>
            <div>
                <label class="header2_key">Bom Tool</label>
            </div>
            <div>
                <label class="header2_key">RFQ</label>
            </div>
            <div>
                <label class="header2_key">技术资源</label>
            </div>
            <div>
                <label class="header2_key">帮助中心</label>
            </div>

            <div class="flex" style="margin-left: auto;margin-right: 10%;position: relative;">
                更改语言：<span class="lngText" onclick="showLngBoxFn()">中文</span>
                <div style="margin-left: 2vw;font-size: 13px;">
                    货币：
                    <img src="../../images/icons/zhongguo.png" style="width: 1.5vw;object-fit: contain;">
                    <span class="lngText" onclick="showLngBoxFn()"
                        style="color: white;text-decoration: underline;">RMB￥</span>
                </div>
                <!-- 语言切换 & 货币切换盒子 -->
                <div class="showLngBox" id="showLngBox" data-show="false" style="display: none;">
                    <div>更改语言</div>
                    <div class="layui-form">
                        <select class="layui-select">
                            <option value="0">选择语言</option>
                        </select>
                    </div>
                    <div>货币</div>
                    <div style="position: relative;" class="layui-form">
                        <select class="layui-select">
                            <option class="option1">请选择</option>
                        </select>
                    </div>
                    <div style="margin-top: .5vw;">
                        <button class="layui-btn" onclick="confirmLngFn()"
                            style="background-color: var(--blue-deep);border-radius: 30px !important;">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 头部结束 -->
    <div class="_main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
            <div>></div>
            <div onclick="toRouter(this)" data-link="../productCategory/productCategory.html">产品分类</div>
            <div>></div>
            <div onclick="toRouter(this)" data-link="../provider/modulesSearch.html">xx模块
            </div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="../product/productDetails.html">PM01
            </div>
        </div>
    
        <!-- 正片开始 -->
        <div class="productBox">
            <div class="left flex">
                <div>
                    <div><img src="../../images/icons/device3.png" alt="产品图片"></div>
                    <div><img src="../../images/icons/device3.png" alt="产品图片"></div>
                    <div><img src="../../images/icons/device3.png" alt="产品图片"></div>
                    <div><img src="../../images/icons/device3.png" alt="产品图片"></div>
                    <div><img src="../../images/icons/device3.png" alt="产品图片"></div>
                </div>
                <div>
                    <div>
                        <img src="../../images/icons/device3.png" alt="">
                    </div>
                </div>
            </div>
            <div class="right">
                <div class="productTitle">220V转5V3.3V9V12V15V24V电源模块HLK-PM01 ACDC隔离电源稳压输出</div>
                <div class="flex"> <div>原厂编号:    </div>     <div class="textOver" style="margin-left: auto; width: 60%;">727-S40FC008C3B1V000  </div>      </div>
                <div class="flex"> <div>制造商:      </div>     
                    <div class="textSelect pointer" style="margin-left: auto; width: 60%;" onclick="toRouter(this)" 
                    data-link="../../pages/provider/providerDetail.html">Hi-Link</div>      
                </div>
                <div class="flex"> <div>制造商编号:  </div>     <div class="textOver" style="margin-left: auto; width: 60%;">727-S40FC008C3B1V000  </div>      </div>
                <div class="flex"> <div>型号:       </div>      <div class="textOver" style="margin-left: auto; width: 60%;">HLK-PM01             </div>      </div>
                <div class="flex"> <div>封装:       </div>      <div class="textOver" style="margin-left: auto; width: 60%;">插件                 </div>       </div>
                <div class="flex"> <div>包装方式:   </div>      <div class="textOver" style="margin-left: auto; width: 60%;">盒装                  </div>      </div>

                <div class="starBox">
                    <div id="ID-rate-demo"></div> 4.0 3820 个评价 | 33人购买
                </div>
                <script>
                    layui.use(function(){
                      var rate = layui.rate;
                      // 渲染
                      rate.render({
                        elem: '#ID-rate-demo'
                      });
                    });
                </script>
                <div class="configBox">
                    <div style="width: 100%;">型号:HLK-PM01</div>
                    <div onclick="selectDiv(this)" data-select="true">HLK-PM01</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M05</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M09</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M12</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M15</div>
                    <div onclick="selectDiv(this)" data-select="false">HLK-3M24</div>
                </div>
                <div class="booksBtnBox">
                    <button class="button">  <i class="iconfont icon-pdf"></i>  规格书</button>
                    <button class="button">  <i class="iconfont icon-tubiao_daochuCAD"></i>  CAD工具</button>
                </div>
            </div>
        </div>
        <!-- 产品推荐 -->
        <div class="productComment">
            <div class="title">
                产品推荐
            </div>
            <div class="mainBox2_container">
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star">4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
            </div>
        </div>
       
    </div>
     <!-- 商品参数 / 用户评价 -->
    

    <div class="optionsBox2">
        <div class="titleBox1 flex">
            <div data-select="false" onclick="titleClick(this,1)" class="titleSelect" style="margin-left: 1vw;">商品参数</div>
            <div data-select="false" onclick="titleClick(this,2)" style="margin-left: 20px;">用户评价</div>
        </div>
    </div>
    <script>
        function titleClick(dom,index) {
            $(dom).siblings().removeClass('titleSelect');
            $(dom).addClass('titleSelect');
            if ( index == 1) {
                $('.optionsBox2').nextUntil('.goodsDataTitle1').hide();
                if ($('.goodsDataTitle1')[0].style.display === 'none') {
                    $('.commentBox').nextUntil('.bug').show();
                }
            }else{
                $('.commentBox').nextUntil('.bug').hide();
                if ($('.commentHeader')[0].style.display === 'none') {
                    $('.optionsBox2').nextUntil('.goodsDataTitle1').show();
                }
            }
            
        }
    </script>
    <!-- 评论头 -->
    <div class="commentHeader" style="display: none;">
        <div class="titleBox">
            <div>用户评价</div>
        </div>
        <div class="optionsBox flex">
            <div data-select="false" onclick="selectDiv(this,'titleSelect')" class="titleSelect" style="margin-left: 0px;">全部</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">有图</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">追评</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">好评</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">差评</div>
            <div style="margin-left: auto;">默认排序 <i class="iconfont icon-xiangxia"></i> </div>
        </div>
    </div>
    <!-- 评论区 -->
    <div class="commentBox" style="display: none;">
        <div class="commentContainer">
            <div class="commentContent">
                <!-- 每一条评论 -->
                <div class="comment">
                    <div class="commentUserInfo flex">
                        <div class="avatar" style="width: 5%;">
                            <img src="../../images/icons/zhanghao.png" alt="">
                        </div>
                        <div class="userInfo">
                            <div class="userName">张三</div>
                            <div>1天前 · HLK-5M05【220V转5V1A-5W】</div>
                        </div>
                        <div class="iconfont icon-icon" style="margin-left: auto;">有用(6)</div>
                    </div>
                    <div class="commentText">
                        很好，下次还会回购
                        <div class="imageBox">
                            <img src="../../images/icons/device.png" alt="">
                            <img src="../../images/icons/device.png" alt="">
                        </div>
                    </div>
                </div>
                <div class="comment">
                    <div class="commentUserInfo flex">
                        <div class="avatar" style="width: 5%;">
                            <img src="../../images/icons/zhanghao.png" alt="">
                        </div>
                        <div class="userInfo">
                            <div class="userName">张三</div>
                            <div>1天前 · HLK-5M05【220V转5V1A-5W】</div>
                        </div>
                        <div class="iconfont icon-icon" style="margin-left: auto;">有用(6)</div>
                    </div>
                    <div class="commentText">
                        很好，下次还会回购
                        <div class="imageBox">
                            <img src="../../images/icons/device.png" alt="">
                            <img src="../../images/icons/device.png" alt="">
                        </div>
                    </div>
                </div>
                <!--  -->
                <div class="allComments">
                   <span>  查看全部 <i class="iconfont icon-a-zoomline"></i> </span>
                </div>
            </div>
            <!--  -->
        </div>
    </div>
    <!-- & 商品参数 -->
    
    <div class="goodsDataTitle0 goodsDataTitle1 flex">商品参数
        <button class="button button_blue" style="margin-left: auto;">查看类似产品</button>
    </div>
    <div class="goodsDataTable">
        <table style="width: 100%;text-align: center;border: 1px solid #BABABA;overflow: hidden;">
            <colgroup>
                <col width="100">
            </colgroup>
            <thead>
                <tr style="background-color: var(--text-color4);">
                    <th style="padding: 1vw;">
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked> 
                        </span>
                    </th>
                    <th>梯度</th>
                    <th>原价</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked> 
                        </span>
                    </td>
                    <td>1+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked> 
                        </span>
                    </td>
                    <td>10+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked> 
                        </span>
                    </td>
                    <td>20+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked> 
                        </span>
                    </td>
                    <td>50+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked> 
                        </span>
                    </td>
                    <td>100+</td>
                    <td>￥3.88</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="BBB" checked> 
                        </span>
                    </td>
                    <td>200+</td>
                    <td>￥3.88</td>
                </tr>
            </tbody>
        </table>
        <div class="likes flex"><div style="margin-left: auto;">2774个相似产品</div></div>
    </div>
    <div class="goodsDataTitle1 flex">产品手册PDF
        <button class="button button_blue" style="margin-left: auto;">下载PDF</button>
    </div>
    <div class="profilePDF">
        <iframe src="../..//images/files/pdf.pdf"></iframe>
    </div>
    <div class="goodsDataTitle2 goodsDataTitle1" style="padding: 1vw;margin-top: 2vw;">购物指南</div>

    <div class="shoppingGuide goodsDataTitle3">
        <img src="../../images/pics/02.png" alt="">
    </div>
    <div class="bug"></div>
    <!-- 底部开始 -->
    <div class="footer">
        <div class="footer2">
            <div class="footer1Content">
                <div>深圳市海凌科电子有限公司</div>
                <div>电话 : 0755-23152658</div>
                <div>邮箱 : <EMAIL></div>
                <div>地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>公司</div>
                <div>关于我们</div>
                <div>新闻中心</div>
                <div>品质保证</div>
                <div>提交工单</div>
                <div>企业社会责任</div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>资源</div>
                <div>新品速递</div>
                <div>代理招商</div>
                <div>应用场景</div>
                <div>服务和工具</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>支持</div>
                <div>联系我们</div>
                <div>帮助</div>
                <div>反馈</div>
                <div>Cookie政策</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <!--  -->
            <div class="footer1Content">
                <div class="connectOur">联系我们</div>
                <div class="footerLogo">
                    <img src="../../images/icons/weixin-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/youjian-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/wangzhi-landi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/skype-baidi.png" alt="" style="height: 100%;">
                </div>
                <div><img src="../../images/icons/gzh.png" alt="" style="width: 5vw;max-width: 75px;"></div>
                <div>关注微信订阅号</div>
                <div></div>
                <div></div>
            </div>
        </div>
        <div class="footer3">
            联系电话 : 0755-23152658 版权所有 : 深圳市海凌科电子有限公司 备案号 : 粤ICP备12055399号-1 技术支持 : 极思灵创
        </div>
    </div>
    <!-- 底部结束 -->
  <aside class="orderAside" style="display: none;">
      <div class="asideTitle"> 
          <i class="iconfont icon-zhekou-shi"><span style="margin-left: .5vw;">5件95折</span></i>
          <i class="iconfont icon-chevron-right" style="margin-left: auto;"></i>
      </div>
      
      <div class="price">
          <div><b style="font-size: 1.7vw;">¥5.99 </b> <span class="gray">¥5.99</span> <span class="discount">- 63%</span></div>
          <div><div class="gray" style="font-size: 15px;">税前价格</div></div>
      </div>

      <div class="asideTextTitle">阶梯价格</div>
      <div class="tableBox">
          <table style="height: 8vw;width: 100%;text-align: center;">
              <thead>
                  <tr style="background-color: var(--text-color4);">
                      <th style="padding: 0.2vw;">梯度</th>
                      <th style="padding: 0.2vw;">原价</th>
                      <th style="padding: 0.2vw;">售价</th>
                      <th style="padding: 0.2vw;">折合1盒</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                      <td>1+</td>
                      <td>￥3.88</td>
                      <td>￥3.48</td>
                      <td>￥228</td>
                  </tr>
                  <tr>
                      <td>10+</td>
                      <td>￥3.88</td>
                      <td>￥3.48</td>
                      <td>￥228</td>
                  </tr>
                  <tr>
                      <td>20+</td>
                      <td>￥3.88</td>
                      <td>￥3.48</td>
                      <td>￥228</td>
                  </tr>
                  <tr>
                      <td>50+</td>
                      <td>￥3.88</td>
                      <td>￥3.48</td>
                      <td>￥228</td>
                  </tr>
                  <tr>
                      <td>100+</td>
                      <td>￥3.88</td>
                      <td>￥3.48</td>
                      <td>￥228</td>
                  </tr>
                  <tr>
                      <td>200+</td>
                      <td>￥3.88</td>
                      <td>￥3.48</td>
                      <td>￥228</td>
                  </tr>
              </tbody>
          </table>
      </div>
      <div class="asideTextTitle">库存总量(单位: 个)</div>
      <div class="tableBox">
          <table style="height: 2.5vw;width: 100%;text-align: center;">
              <thead>
                  <tr style="background-color: var(--text-color4);">
                      <th style="padding: 0.2vw;">内陆仓</th>
                      <th style="padding: 0.2vw;">海外仓</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                      <td>8,960</td>
                      <td>7,160</td>
                  </tr>
              </tbody>
          </table>
      </div>
      <div></div>
     <div class="asideTextTitle" style="padding-top: 0vw;">购买数量(100个/盒)</div>
     <!-- 计数器 -->
     <div class="computer"> 
          <div class="iconfont icon-jianhao" onclick="compute(0,10,0)"></div> 
          <div style="flex: 1;" id="count"> 1 </div>
          <div class="iconfont icon-tianjia1" onclick="compute(1,10,0)"></div> 
      </div>
     <div class="gray" style="padding-top: 2px;">起订量:10 增量:10</div>
     <div class="asideTextTitle totalPrice">总价：￥36.48</div>
     <div>
          <button class="button asideBtn" style="color: var(--blue-deep);">加入购物车</button>
      </div>
     <div style="padding: 0px;">
          <button class="button asideBtn button_blue">立即购买</button>
      </div>

     <div class="flex aside_btnBox2" style="justify-content: space-around;flex-wrap: wrap;padding-top: 1.2vw;">
          <div><i class="iconfont icon-aixin2 pointer" style="color: var(--blue-deep);"></i>加入心愿单</div>
          <div><i class="iconfont icon-share pointer" style="color: var(--blue-deep);padding-right:0.5vw;"></i>分享链接</div>
     </div>
      <!-- 右侧边栏 -->
    <script>
      function handleScroll() {
            // 到顶 或者到底 就隐藏
            if (isAtTop() || isAtBottom()) {
                $('.orderAside').fadeOut(300);
            }else{
                $('.orderAside').fadeIn();
            }
            // 处理滚动事件的逻辑
        }

        // 节流处理滚动事件
        const throttledScroll = throttle(handleScroll, 200);
        window.addEventListener('scroll', throttledScroll);
      // document.addEventListener('mousewheel', function (e) {
         
      // })
  </script>
  </aside>
  <!-- <img src="../../images/pics/01.png" class="img" alt=""> -->
  <!-- 服务申明 -->
  <div class="serverInfo">
      <div class="serverTitle" style="font-size: 1vw;">服务申明</div>
      <div class="serverTitle flex"> 
        <div class="iconfont icon-sign" style="font-size: 1.5vw;margin-left: .2vw;margin-right: .2vw;"></div>
        <div>快递</div>
      </div>
      <div class="serverItem flex">
        <div class="iconfont icon-ai210"></div><div>支持七天无理由退货</div>
      </div>
      <div class="serverItem flex">
        <div class="iconfont icon-ai210"></div><div>如果快递丢失，支持退货</div>
      </div>
      <div class="serverItem flex">
        <div class="iconfont icon-ai210"></div><div>如果快递损坏，支持退货</div>
      </div>
      <div class="serverItem flex">
        <div class="iconfont icon-ai210"></div><div>支持90天内免费换货</div>
      </div>

      <div class="serverTitle" > <i class="iconfont icon-secured" style="font-size: 1.5vw;"></i> 安全与隐私</div>
      <div class="serverItem">
        <div>安全付款:未经您的同意，我们不会与任何第三方分享您的个人信息。</div>
        <div>安全的个人资料:我们保护您的隐私，确保您的个人资料安全可靠。</div>
      </div>

      <div class="serverTitle" >
         <i class="iconfont icon-money-circle" style="font-size: 1.5vw;"></i> 
         支付安全</div>
      <div class="serverItem">
        <div class="paymentMethods flex" style="justify-content: left;margin-bottom: .5vw;">
            <!-- <img src="../../images/icons/zhifubao.png" alt=""> -->
            <img src="../../images/icons/paypal.png" alt="">
            <img src="../../images/icons/weixin.png" alt="">
        </div>
        <div>与受欢迎的支付合作伙伴合作，您的个人信息是安全的。</div>
      </div>
  </div>
</div>
    
</body>
</html>
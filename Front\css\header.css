/* 头部样式  -公共 */
.header {
    width: 90%;
    padding: 1vw 5% 1.5vw 5%;
    display: flex;
    justify-content: center;
    /* border: 1px solid red; */
    margin: auto;
    border-bottom: 1px solid var(--line);
}

.headerItem {
    /* width: 200px; */
    padding: 5px 10px;
    height: 1.8vw;
    margin: 5px;
    font-size: 1.0em;
    /* border: 1px solid red; */
    display: flex;
    justify-content: space-between;
    place-items: center;
    position: relative;
}

.headerItem>.hoverText:hover {
    transition: all .3s;
    color: black;
    cursor: pointer;
}

.header_keyBox {
    position: absolute;
    top: 115%;
    left: 2%;
    padding-bottom: 10px;
    font-size: 12.5px;
    color: var(--text-color);
}

.header_key:hover {
    color: var(--blue-deep);
}

/* 关键字 input 用于判定是否选中 */
.header_keyCheck[type="radio"] {
    display: none;
}

/* 关键字 */
.header_key {
    padding: 0px 10px;
    font-size: var(--size0);
    cursor: pointer;
    transition: all .3s;
}

/* 当对应的单选按钮被选中时，改变菜单按钮的背景颜色 */
.header_keyCheck[type="radio"]:checked+.header_key {
    color: var(--blue);
}
.headLogo{
    max-width: 137px;
    width: 10%;
    margin-right: 1%;
}

.searchBox {
    width: 100%;
    height: 2vw;
    min-height: 30px;
    border: 2px solid var(--blue-deep);
    box-sizing: border-box;
    border-radius: 50px;
    /* overflow: hidden; */
    background-color: var(--bd);
    display: flex;
    place-items: center;
}


/* 取消掉产品分类select的默认样式 */
.headerForm>.layui-form-select>.layui-select-title>.layui-input {
    all: unset;
    user-select: none;
    text-align: center;
    width: 90%;
    font-size: .9em;
}

.headerForm>.layui-form-select {
    margin-top: -3px;
    width: 5vw !important;
    min-width: 100px !important;
    user-select: none;
    /* border: 1px solid red;*/
}

.searchInput {
    border: none !important;
    padding: 0px 10px;
    font-size: 14px;
    width: 100%;
    outline: none;
    box-shadow: none;
    color: var(--text-color);
    background-color: transparent;
}

.searchInput::placeholder {
    font-size: 13px !important;
    letter-spacing: 1px;
}

.searchButton {
    width: 25%;
    min-width: 80px;
    background-color: var(--blue-deep);
    height: 100%;
    border-radius: 0 15px 15px 0px;
    color: white;
    border: none;
}

.header2 {
    padding: .1vw 0px;
    display: flex;
    place-items: center;
    /* gap: .8vw; */
    background-color: var(--blue-deep);
    color: white;
    position: relative;
    overflow: visible;
    /* box-shadow: 0 0 4px #ccc; */
}

.header2>.headerForm>.layui-form-select>.layui-select-title>.layui-input {
    color: white;
}

.header2>div {
    padding: .2vw;
    height: 80%;
    /* border: 2px solid ; */
}

/* header2 */
.header2_key:hover {
    color: var(--line);
}

/* 关键字 */
.header2_key {
    padding: 0px 10px;
    height: 40px !important;
    line-height: 40px;
    cursor: pointer;
    transition: all .3s;
}

.lngText {
    cursor: pointer;
}



.showLngBox {
    position: absolute;
    top: 140%;
    right: -10%;
    min-width: 200px;
    min-height: 160px;
    width: 14vw !important;
    height: auto !important;
    max-height: 260px;
    background-color: white;
    z-index: 99999;
    border-radius: 15px;
    border: 1px solid #E2E3E9;
    box-shadow: 4px 2px 8px 0px rgba(0, 0, 0, 0.15), -4px -2px 8px 0px rgba(0, 0, 0, 0.15);
    padding: 1.5vw 1vw;
    color: var(--text-color);
}

/* .showLngBox>div {
    padding: 1vw 1vw;
} */

.showLngBox>div:nth-child(2n-1) {
    font-size: 1vw;
    text-indent: 20px;
}

.showLngBox>div:nth-child(2n) {
    padding: .35vw 0vw;
    margin-left: 20px;
}

.productCategory {
    position: relative;
    border-bottom: 1px solid transparent;
}

/* 取消掉产品分类四个字发光 */
/* .productCategory:hover {
    color: var(--blue-deep);
    border-bottom: 1px solid var(--blue-deep); 
} */
.productCategory:hover .productCategoryBox {
    display: block;
}

/* 产品分类 开始 */
.productCategoryBox {
    position: absolute;
    top: 100%;
    left: 0%;
    width: 16vw;
    height: auto;
    min-height: 660px;
    /* padding: 1vw 0px; */
    background-color: white;
    z-index: 99999999999999;
    border-radius: 25px;
    /* box-shadow: 0 0 40px #ccc; */
    display: none;
    /* overflow-y: scroll; */
}

.productCategory_onePage {
    position: absolute;
    width: 100%;
    height: 36vw;
    background-color: white;
    overflow-y: scroll;
    border-radius: 10px;
    z-index: 2;
}

.productCategory_onePage>div {
    padding: 15px 0px;
    display: flex;
    justify-content: space-between;
    color: var(--text-color);
    cursor: pointer;
    white-space: nowrap;
    text-align: left;
    font-size: 1em;
    text-indent: 1.7cqw;
}
.productCategory_onePage:hover ~ .productCategory_hoverPages{
    display: flex;
}

.productCategory_hoverPages {
    position: relative;
    left: 100%;
    top: 0%;
    width: 65vw;
    height: 36vw;
    background-color: white;
    overflow: hidden;
    /* display: flex; */
    display: none;
    z-index: 2;
    /* border-radius: 0px 25px 25px 0px; */
    border: 1px solid var(--line);
    border-radius: 0px 10px 10px 0px;
}

.productCategory_onePage>div:hover {
    color: var(--blue-deep);
}

.productCategory_hoverPages>div>div:hover {
    color: var(--blue-deep);
}

.productCategory_onePage:hover~.hoverPages {
    display: flex !important;

}

.productCategory_hoverPages:hover {
    display: flex;
}

.productCategory_hoverPages>div>div {
    padding: 16px 0px;
    display: flex;
    justify-content: space-between;
    color: var(--text-color);
    cursor: pointer;
    white-space: nowrap;
    text-align: left;
    font-size: 1em;
    text-indent: 1.7cqw;
}

.twoPage {
    flex: 3;
    overflow-y: scroll;
}

.twoPage>div[data-select="true"] {
    color: var(--blue-deep);
    /* background-color: var(--text-color4); */
}

.productCategory_onePage>div[data-select="true"] {
    color: var(--blue-deep);
    /* background-color: var(--text-color4); */
}

.threePageMore {
    position: absolute;
    top: 100px;
    right: 10vw;
    font-size: .9vw !important;
}




.threePage {
    top: 0%;
    left: 22%;
    flex: 9;
    height: 100%;
    z-index: 2;
    background-color: white;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    overflow-x: auto;
    /* border: 1px solid var(--line); */
    border-radius: 0px 25px 25px 0px;
}

.threePage_contentBox {
    flex: 1;
    height: 3vw;
    display: flex;
    place-items: center;
    /* border: 1px solid red; */
}

.threePage_contentBox>img {
    margin-left: 2vw;
    max-width: 100px;
    height: 100%;
    object-fit: contain;
}

.threePage_contentBox_name {
    margin-left: 0px;
    width: 100%;
    text-align: left;
    color: var(--blue-deep);
}

/* 产品分类结束 */


/* z账户 */
.acountBox:hover .acountInfo{
    display: flex;
}
.acountInfo{
    position: absolute;
    top: 90%;
    width: 150%;
    padding: 1vw;
    border: 1px solid var(--line);
    box-shadow: 4px 2px 8px 0px rgba(0,0,0,0.15);
    border-radius: 24px 24px 24px 24px;
    border: 1px solid #E2E3E9;
    display: flex;
    flex-direction: column;
    background-color: white;
    z-index: 99999999999999;
    display: none;
}

.acountHead{
    /* padding: .2vw 0px 1vw 0px; */
    height: 100%;
    /* border-bottom: 1px solid var(--line); */
}
.acountHead>div:nth-child(1){
    width: 30%;
    margin-right: auto;
}
.acountHead>div:nth-child(2){
    width: 65%;
    /* border: 1px solid ; */
}
.acountHead>div>img{
    width: 100%;
}
.acountArea>div>.icon{
    margin-right: .7vw;
}
.acountArea{
    padding-bottom: 1vw;
    margin: 5px;
    border-bottom: 1px solid var(--line);
}
.acountArea>div{
    padding: .25vw 0px;
    font-size: .75vw;
    display: flex;
    justify-content: left;
    place-items: center;
}
.acountArea>div:hover{
    color: var(--blue-deep);
    cursor: pointer;
}
.loginOutBox>div{
    padding: .5vw;
    border-radius: 20px;
    cursor: pointer;
    text-align: center;
    box-sizing: border-box;
    border: 1px solid transparent;
}
/* .loginOutBox>div:hover{
    background-color: var(--blue-deep);
    border: 1px solid var(--line);
    color: white;
} */
.loginOut{
    margin-left: auto;
    color: var(--red);
    text-decoration: underline;
    font-size: .5vw;
    cursor: pointer;
    display: none;
}
.acountHead[data-login="true"]{
    display: flex;
    /* display: none; */
}
.acountHead[data-login="false"]{
    display: none;
    /* display: flex; */
}
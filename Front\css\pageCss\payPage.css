.pageBox {
    width: 100%;
    background-color: #f5f5f5;
}

.header {
    width: 100%;
    background-color: white;
    display: flex;
    place-items: center;

}

.logo {
    width: fit-content;
    padding: 1vw 0px;
    background-color: white;
    display: flex;
    place-items: center;
}

.main {
    margin-left: 20%;
    padding: 1vw 0px 2vw 0px;
    width: 60%;
    /* height: 70vh; */
}

.stepBox {
    padding: 0vw 1;
}

.checkOrderImage {
    object-fit: contain;
    width: 137px;
    margin-left: 20vw;
}

/* 步骤条 */
.stepBoxContainer {
    margin-left: auto;
    width: 450px;
    margin-right: 20vw;
}

.stepBox {
    width: calc(100% - 20px);
    padding: 0vw 10px;
    display: flex;
}

.stepBox>.item {
    flex: 1;
    position: relative;
    /* border: 1px solid ; */
    padding: 10px 0px;
    justify-content: right;
}

.stepText {
    position: absolute;
    right: -30px;
    top: 85%;
    width: fit-content;
    white-space: nowrap;
    font-size: 16px;
}

.stepLine {
    flex: 1;
    height: 3px;
    border: 1px solid white;
    border-radius: 5px;
    transition: 1s;
    margin: 0px 10px;
    position: relative;
    background-color: var(--text-color4);
}

.circle {
    border-radius: 50%;
    width: 28px;
    height: 28px;
    margin-bottom: 5px;
    background-color: var(--text-color4);
    color: white;
    display: flex;
    justify-content: center;
    place-items: center;
}

.number {
    font-size: 20px;
}

.current>.stepLine {
    background-color: var(--hlk-red);
}

.current>.circle {
    background-color: var(--hlk-red);
    color: white;
}

.current>.stepText {
    color: var(--hlk-red);
}

/* 主体 */
.checkOrderMain {
    padding: 20px 0 20px 10px;
    display: flex;
    width: 60%;
    background-color: #f5f5f5;
    /* border: 1px solid ; */
    margin-left: 20%;
}

.checkOrderMain>.left {
    width: 70%;
}

.checkOrderMain>.right {
    /* margin: 0px 10px 0px 20px; */
    margin: 0 auto;
    width: 250px;
}

.left>.box {
    margin-top: 20px;
    background-color: white;
    padding: 20px;
}
.payMethodsBox{
    flex-wrap: wrap;
    display: flex;
    place-items: center;
    padding: 20px;
}
.payMethod{
    justify-content:unset;
    flex: 1;
}
.payMethod:nth-child(4n){
    margin-top: 20px;
}
input[type="radio"]{
    margin-right: 10px;
}
label{
    margin-right: 10px;
    display: block;
}

#expressInput {
    width: 180px;
    margin-left: 30px;
    height: 30px;
    background-color: white !important;
    border: 1px solid var(--text-color4) !important;
    border-radius: 5px;
}
/* <!-- 右侧边栏 --> */
.aside {
    margin-top: 20px;
    width: 100%;
    /* border-radius: 10px; */
    background-color: white;
    padding: 20px;
    /* box-shadow: 0 0 4px #ccc; */
}

.asideItem {
    display: flex;
    margin-bottom: 5px;
}

.asideItem>div:first-child {
    font-size: 15px;
}

.asideItem>div:last-child {
    margin-left: auto;
}

/* 服务申明 */
.serverInfo {
    width: 100%;
    color: var(--text-color1);
}

.serverInfoIcon {
    font-size: 20px;
}

.serverTitle {
    padding: 5px 0 2px 0px;
    font-size: 13px;
    color: var(--text-color);
    justify-content: left;
}

.serverItem {
    margin-left: 1.5vw;
    justify-content: left;
    font-size: 11px;
}

.serverItem>div:last-child {
    color: var(--text-color2);
}

.serverItem>.icon-ai210 {
    color: var(--blue-deep);
}

.paymentMethods>img {
    margin-right: .2vw;
}

.coupon {
    padding: 10px 5px 0 5px;
}

.coupon>input {
    padding: 4px;
    border: 1px solid var(--line);
    color: var(--text-color1);
    letter-spacing: 2px;
}

.coupon>button {
    padding: 3px 10px;
    outline: none;
    border: none;
}
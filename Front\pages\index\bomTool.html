<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
</head>

<body>
    <div class="pageBox"></div>
    <!-- 头部开始 -->
    <div class="header">
        <div class="headerItem" style="width: 10vw;">
            <img src="../../images/icons/hlk.png" style="object-fit: contain;width: 137px;">
        </div>
        <div class="headerItem" style="width: 35%;">
            <div class="searchBox flex">
                <span class="layui-form headerForm">
                    <select class="select" name="select" id="" style="font-size: 1em;" title="分类">
                        <option value="1">商家</option>
                        <option value="2">品牌</option>
                        <option value="3">型号</option>
                        <option value="4">产品规格</option>
                    </select>
                </span>
                <span class="line"></span>
                <input class="input searchInput" type="text" placeholder="请输入产品规格/型号/品牌/商家">
                <button class="button searchButton flex" style="font-size: .9vw;">
                    <div class="iconfont icon-search" style="margin-right: 5px;font-size: 1vw;"></div>
                    搜索
                </button>
                <!-- 推荐关键字 -->
                <div class="header_keyBox flex">
                    <input class="header_keyCheck" type="radio" id="option1" name="menuOptions" checked>
                    <label class="header_key textOver" for="option1">菜单1</label>

                    <input class="header_keyCheck" type="radio" id="option2" name="menuOptions">
                    <label class="header_key textOver" for="option2">菜单2</label>

                    <input class="header_keyCheck" type="radio" id="option3" name="menuOptions">
                    <label class="header_key textOver" for="option3">菜单3</label>
                </div>
            </div>
        </div>
        <div class="headerItem" style="font-size: .9vw;min-width: 100px;width: 10%;">
            <div class="flex textOver">
                <input type="checkbox" name="" id="goods1">
                <label for="goods1" style="margin: -4px 5px;">库存量</label>
            </div>
            <div class="flex textOver" style="margin-left: auto;">
                <input type="checkbox" name="" id="goods2">
                <label for="goods2" style="margin: -4px 5px;">PHUJ</label>
            </div>
        </div>
        <div class="headerItem" style="color: var(--text-color);font-size: 1.1em;margin-left: 7%;">
            <i class="iconfont icon-weidenglu icon" style="font-size: 1.4em;margin-right: 12px;"></i>
            <span class="hoverText textOver" onclick="toRouter(this)" data-link="../login/login.html">登录</span>/
            <span class="hoverText textOver" onclick="toRouter(this)" data-link="../login/register.html">注册</span>
        </div>
        <div class="headerItem" style="color: var(--text-color);font-size: 1.1em;">
            <i class="iconfont icon-cart icon" style="font-size: 1.4em;margin-right: .2em;"></i>
            <span class="hoverText textOver">购物车</span>
        </div>
    </div>
    <!-- 头部分类  style="background-color: white;color: black;"-->
    <div class="header2 textOver">
        <div class="flex productCategory" data-select="false" onclick="isSelect_productCategory(this)"
            style="margin-left: 10%;user-select: none;cursor: pointer;">
            <label for="select" class="iconfont icon-fenlei2" style="font-size: 26px;">
            </label> 产品分类 <i class="iconfont icon-xiangxia"></i>

            <div class="productCategoryBox">
                <!-- 一级页面 -->
                <div class="productCategory_onePage">
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> ACDC电源模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                    </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> DCDCD电源模组</span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                    </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> 雷达模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> WIFI模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> BLE模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> 路由模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> 人脸识别 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                            class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                </div>
                <script>
                    // 监听鼠标hover事件
                    var productCategoryBox_dom = $(".productCategoryBox");
                    productCategoryBox_dom.mouseover(function (e) {
                        if (e.target.getAttribute("data-select")) {
                            $(e.target).siblings().attr("data-select", "false");
                            e.target.setAttribute("data-select", "true");
                        }
                    })
                </script>

                <div class="productCategory_hoverPages">

                    <div class="twoPage">
                        <div data-select="true" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> ACDC电源模组 </span>
                        </div>
                        <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> DCDCD电源模组</span> </div>
                        <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> 雷达模块 </span> </div>
                        <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> WIFI模块 </span> </div>
                        <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> BLE模块 </span> </div>
                        <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> 路由模块 </span> </div>
                        <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> 人脸识别 </span> </div>
                        <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> </div>
                        <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> </div>
                        <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span>
                        </div>
                    </div>

                    <div class="threePage">
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device.png">
                            <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device.png">
                            <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device.png">
                            <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device.png">
                            <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device2.png">
                            <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device2.png">
                            <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>

                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device2.png">
                            <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>

                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device2.png">
                            <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device.png">
                            <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device.png">
                            <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device.png">
                            <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device.png">
                            <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device2.png">
                            <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>
                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device2.png">
                            <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>

                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device2.png">
                            <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>

                        <div class="threePage_contentBox" onclick="changeSelect(this)">
                            <img src="../../images/icons/device2.png">
                            <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                    style="color: var(--text-color);">(128)</span> </div>
                        </div>

                    </div>
                    <div class="threePageMore">更多</div>
                </div>
                <!--  -->
            </div>
        </div>
        <span class="line2" style="height: 17px;margin-left: 10px;"></span>
        <div onclick="toRouter(this)" data-link="../index/index.html">
            <label class="header2_key">首页</label>
        </div>
        <!-- 第一版 -先干到海凌科品牌商去 -->
        <div onclick="toRouter(this)" data-link="../provider/providerDetail.html">
            <!-- <div onclick="toRouter(this)" data-link="../provider/provider.html"> -->
            <label class="header2_key">供应商</label>
        </div>
        <div>
            <label class="header2_key">Bom Tool</label>
        </div>
        <div>
            <label class="header2_key">RFQ</label>
        </div>
        <div>
            <label class="header2_key">技术资源</label>
        </div>
        <div>
            <label class="header2_key">帮助中心</label>
        </div>

        <div class="flex" style="margin-left: auto;margin-right: 10%;position: relative;">
            更改语言：<span class="lngText" onclick="showLngBoxFn()">中文</span>
            <div style="margin-left: 2vw;font-size: 13px;">
                货币：
                <img src="../../images/icons/zhongguo.png" style="width: 1.5vw;object-fit: contain;">
                <span class="lngText" onclick="showLngBoxFn()"
                    style="color: white;text-decoration: underline;">RMB￥</span>
            </div>
            <!-- 语言切换 & 货币切换盒子 -->
            <div class="showLngBox" id="showLngBox" data-show="false" style="display: none;">
                <div>更改语言</div>
                <div class="layui-form">
                    <select class="layui-select">
                        <option value="0">选择语言</option>
                    </select>
                </div>
                <div>货币</div>
                <div style="position: relative;" class="layui-form">
                    <select class="layui-select">
                        <option class="option1">请选择</option>
                    </select>
                </div>
                <div style="margin-top: .5vw;">
                    <button class="layui-btn" onclick="confirmLngFn()"
                        style="background-color: var(--blue-deep);border-radius: 30px !important;">确定</button>
                </div>
            </div>
        </div>
    </div>
    </div>
    <!-- 头部结束 -->
    <style>
        .main {
            width: 100%;
            padding: 0;
            margin: 0;
            height: 60vh;
            position: relative;
        }

        .bgImage {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .bomToolBox {
            width: 700px;
            height: 370px;
            background-color: white;
            box-shadow: 0px 2px 16px 0px #D3D3D3;
            /* border: 1px solid ; */
        }

        .title {
            margin-top: .5vw;
            text-indent: .5vw;
            font-size: .8vw;
            justify-content: center;
        }

        .titleSelect {
            color: black !important;
        }

        .title>div {
            font-size: 17px;
            padding: 5px 0 10px 0px;
            color: var(--text-color2);
        }

        .contentBox {
            padding-top: 10px;
            margin: 20px 20px 10px 20px;
            height: 65%;
            background-color: #F8F9FE;
        }

        #bomText {
            width: 100%;
            height: 100%;
            background-color: #F8F9FE;
            border: none;
            resize: none;
            padding: 0px 20px !important;
            color: var(--text-color);
            
        }

        #bomText::placeholder {
            color: var(--text-color2);
            font-size: 14px;
            white-space: pre-line;
        }
    </style>
    <div class="main flex">
        <div class="bgImage">
            <img src="../../images/pics/bomTool.png" style="width: 100%;">
        </div>
        <div class="bomToolBox">
            <div class="title flex" style="border-bottom: 1px solid #b3b3b3;">
                <div data-select="false" onclick="titleClick(this,1)" class="titleSelect pointer">BOM文件配单</div>
                <div data-select="false" class="pointer" onclick="titleClick(this,2)" style="margin-left: 50px;">手动输入配单
                </div>
            </div>
            <script>
                function titleClick(dom, index) {
                    $(dom).siblings().removeClass('titleSelect');
                    $(dom).addClass('titleSelect');
                    if ( index == 1) {
                       $('#bomBox1').show();
                       $('.uploadTips').show()
                       $('#bomBox2').hide()
                    }else{
                        $('#bomBox1').hide();
                        $('#bomBox2').show()
                        $('.uploadTips').hide()
                    }

                }
            </script>
            <div class="contentBox flex">
                <div id="bomBox1">
                    <div>请将BOM图片或文件拖拽到框内，或点击按钮上传</div>
                    <div style="margin-top: 20px;" class="flex">
                        <button class="button button_blue" style="padding: 10px 30px !important;" onclick="uploadBom()">
                            <i class="iconfont icon-shangchuan"></i>
                            上传BOM
                        </button>
                    </div>
                    <script>
                        async function uploadBom() {
                            const fileRes = await addFile()
                            console.log(fileRes);
                        }
                    </script>
                </div>
                <div id="bomBox2" style="width: 100%;height: 100%;display: none;">
                    <!-- 不要格式化 -->
                    <div style="width: 92%;height: 80%;">
                        <textarea name="bomText" id="bomText" 
                        placeholder="请输入入号/料号/用量等信息进行匹配 

                        例如：
                        B050BS-1WF3  SIP  100pcs"></textarea>
                    </div>

                   <div style="width: 100%;height: 40%;background-color: white;" class="flex">
                        <button class="button button_blue" style="width: 220px;">
                            多型号配单
                        </button>
                   </div>
                </div>
               
            </div>
            <div class="flex uploadTips" style="padding: 0 20px;font-size: 13px;">
                <div style="color: var(--text-color2);">支持多格式上传，如:xls、xlsx、csv、JPG、PNG、JPEG; 最大文件不能超过10M</div>
                <div style="margin: 0 0px 0 auto;" class="textSelect pointer" onclick="download('','file')">模版下载 <i
                        class="iconfont icon-arrow-down"></i> </div>
            </div>
        </div>
    </div>
    <div class="bug"></div>
    <!-- 底部开始 -->
    <div class="footer">
        <div class="footer2">
            <div class="footer1Content">
                <div>深圳市海凌科电子有限公司</div>
                <div>电话 : 0755-23152658</div>
                <div>邮箱 : <EMAIL></div>
                <div>地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>公司</div>
                <div>关于我们</div>
                <div>新闻中心</div>
                <div>品质保证</div>
                <div>提交工单</div>
                <div>企业社会责任</div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>资源</div>
                <div>新品速递</div>
                <div>代理招商</div>
                <div>应用场景</div>
                <div>服务和工具</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>支持</div>
                <div>联系我们</div>
                <div>帮助</div>
                <div>反馈</div>
                <div>Cookie政策</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <!--  -->
            <div class="footer1Content">
                <div class="connectOur">联系我们</div>
                <div class="footerLogo">
                    <img src="../../images/icons/weixin-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/youjian-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/wangzhi-landi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/skype-baidi.png" alt="" style="height: 100%;">
                </div>
                <div><img src="../../images/icons/gzh.png" alt="" style="width: 5vw;max-width: 75px;"></div>
                <div>关注微信订阅号</div>
                <div></div>
                <div></div>
            </div>
        </div>
        <div class="footer3">
            联系电话 : 0755-23152658 版权所有 : 深圳市海凌科电子有限公司 备案号 : 粤ICP备12055399号-1 技术支持 : 极思灵创
        </div>
    </div>
    <!-- 底部结束 -->
    </div>

</body>

</html>
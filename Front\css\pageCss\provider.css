/* 供应商css */
.main{
    margin-left: 15%;
    width: 70%;
}
.title{
    padding: .8vw 0px;
    font-size: 1.2vw;
}
.inputBox>input{
    width: 20%;
}
.checkBox_box{
    padding: 1vw 0px;
    width: fit-content;
}
.checkBox_box>div:nth-child(n+2){
    margin-left: 1vw;
}
.providersBox{
    height: auto;
   /* border: 1px solid #D9D9D9; */
}
.rangeBox{
    padding: .5vw 0px;
    justify-content:left;
    background-color: #D9D9D9;
}
.rangeBox>div{
    margin-left: 1.5vw;
    font-size: 1vw;
    cursor: pointer;
    transition: all .1s;
}
.rangeBox>div:hover{
    color: white;
}
.everyRangeBox{
    border: 1px solid #D9D9D9;
    overflow: auto;
    max-height: 800px;
    /* background-color: red; */
    /* max-height: 60vh; */
}
.everyRange{
    padding: 1vw 0px;
    display: flex;
    flex-direction: column;
    /* height: 30%; */
}
.rangeTitle{
    padding: .5vw 0px;
    text-indent: 1.5vw;
    font-size: 1.2vw;
    background-color: #D9D9D9;
}
.rangeItemContainer{
    /* margin-left: 1.5vw; */
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
}
.rangeItem{
   width: 25%;
   height: 8vw;
   flex-direction: column;
   border-right: 1px solid var(--line);
   border-bottom: 1px solid var(--line);
   box-sizing: border-box;
   cursor: pointer;
}
.rangeItem:nth-child(4n){
    border-right: none;
}
.rangeItem>img{
    max-width: 140px;
    object-fit: contain;
}
.providerName{
    margin-top: 10%;
    text-align: center;
}
.layui-input::placeholder{
    font-size: 13px;
}
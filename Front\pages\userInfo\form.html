<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <!-- 第三方css -->
  <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
  <link rel="stylesheet" href="../../modules/layui/css/layui.css">
  <link rel="stylesheet" href="../../css/public.css">
  <script src="../../modules/layui/layui.js"></script>
</head>

<body>
  <style>
    .editAddrBox {
      width: 30vw;
      max-width: 500px;
      min-width: 400px;
      max-height: 80vh;
      overflow: auto;
      padding: 1vw 1vw .2vw 1vw;
    }

    .distributionBox {
      width: 100%;
      padding: 7px;
      justify-content: left;
      background-color: #F2F2F2;
    }
    .layui-form-label {
      color: var(--text-color2);
    }

    .layui-form-item {
      width: 100%;
      display: flex;
      position: relative;
      /* border: 1px solid var(--line); */
    }

    .layui-inline {
      width: 100%;
      margin-right: 0px !important;
      display: flex;
    }

    .layui-inline>div:last-child {
      margin-right: 0px !important;
      padding-right: 0px !important;
    }

    .layui-input-inline {
      flex: 1;
      /* background-color: red; */
    }
    .failText{
      position: absolute;
      left: 120px;
      top: 92%;
      font-size: 12px;
      color: var(--red);
      display: none;
    }
    .dialog_editAddr>.layui-layer-btn{
      padding: 0px 0px;
      margin: 0px;
      margin-bottom: 20px;
      width: 100%;
      display: flex;
      /* border: 1px solid ; */
      font-size: 17px;
      height: 50px;
    }
    /* 取消按钮 */
    .dialog_editAddr>.layui-layer-btn>a:nth-child(1){
      margin-left: 130px;
      width: 20%;
      min-width: 120px;
      background-color: white;
      color: var(--text-color);
      border: 1px solid #C4C6CF;
      text-align: center;
      height: 40px;
      line-height: 40px;
      border-radius: 7px;
    }
    /* 取消按钮 */
    .dialog_editAddr>.layui-layer-btn>a:nth-child(2){
      margin-left: 40px;
      width: 20%;
      min-width: 120px;
      background-color: var(--blue-deep);
      color: white;
      text-align: center;
      height: 40px;
      line-height: 40px;
      border-radius: 7px;
      margin-right: auto;
    }
    .layui-anim-upbit{
      z-index: 9999;
    }
    .cancelBtn{
      margin-left: 110px;
      width: 25%;
      min-width: 104px;
      background-color: white;
      color: var(--text-color);
      border: 1px solid #C4C6CF;
      text-align: center;
      height: 40px;
      line-height: 40px;
      border-radius: 7px;
    }
    .saveBtn{
      margin-left: auto !important;
      margin-right: auto;
      width: 25%;
      min-width: 104px;
      background-color: var(--blue-deep);
      color: white;
      text-align: center;
      height: 40px;
      line-height: 40px;
      border-radius: 7px;
    }
  </style>
  <div class="editAddrBox">
    <form class="layui-form">
      <div class="layui-form-item">
        <div class="distributionBox flex">
          <div class="layui-form-label" style="padding-top: 0;padding-bottom: 0;">当前配送至</div>
          <div style="margin-left: 10px;">中国大陆</div>
          <div style="margin: 0 5% 0 auto;" class="textSelect pointer">切换</div>
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label required">收货人姓名</label>
          <div class="layui-input-inline layui-input-wrap">
            <input type="tel" name="name1" lay-verify="name1" lay-verType="tips" autocomplete="off" 
              lay-affix="clear" class="layui-input" placeholder="姓氏">
          </div>
          <div class="layui-input-inline layui-input-wrap">
            <input type="tel" name="name2" lay-verify="name2" autocomplete="off" 
              lay-affix="clear" class="layui-input" placeholder="名字">
          </div>
        </div>
        <div class="failText name1">请填写姓氏</div>
        <div class="failText name2" style="left: 65%;">请填写名字</div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label required">手机号码</label>
          <div class="layui-input-inline layui-input-wrap">
            <select name="modules" lay-search>
              <option value="">中国大陆 +86</option>
              <option value="1">layer</option>
              <option value="2">form</option>
              <option value="3">layim</option>
              <option value="4">element</option>
            </select>
          </div>
          <div class="layui-input-inline layui-input-wrap">
            <input type="text" name="phone" lay-verify="phone" autocomplete="off" lay-affix="clear"
              class="layui-input" placeholder="手机号">
          </div>
        </div>
        <div class="failText phone">请输入正确的手机号</div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label required">地址信息</label>
          <div class="layui-input-inline layui-input-wrap">
            <select lay-verify="addr1" name="addr1">
              <option value="">请选择省/市/区/街道</option>
              <option value="北京">北京</option>
              <option value="上海">上海</option>
              <option value="广州">广州</option>
              <option value="深圳">深圳</option>
            </select>
          </div>
        </div>
        <div class="failText addr1">请选择完整的地址信息</div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label required">详细地址</label>
          <div class="layui-input-inline layui-input-wrap">
            <input type="text" name="addr2"  lay-verify="addr2" autocomplete="off" lay-affix="clear"
              class="layui-input" placeholder="请输入详细地址信息">
          </div>
        </div>
        <div class="failText addr2">例如1栋1单元101室</div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">邮政编码</label>
          <div class="layui-input-inline layui-input-wrap">
            <input type="text" name="vercode" autocomplete="off" lay-affix="clear"
              class="layui-input" placeholder="00000">
          </div>
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">公司名称</label>
          <div class="layui-input-inline layui-input-wrap">
            <input type="text" name="vercode" autocomplete="off" lay-affix="clear"
              class="layui-input" placeholder="请输入">
          </div>
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">税号</label>
          <div class="layui-input-inline layui-input-wrap">
            <input type="tel" name="phone" autocomplete="off" lay-reqtext="请填写手机号"
              lay-affix="clear" class="layui-input" placeholder="税号类型">
          </div>
          <div class="layui-input-inline layui-input-wrap">
            <input type="tel" name="phone" autocomplete="off" lay-reqtext="请填写手机号"
              lay-affix="clear" class="layui-input" placeholder="00000">
          </div>
        </div>
        <div class="failText" style="display: block;">例如VAT 12345678</div>
      </div>
      <div class="layui-form-item">
        <div class="layui-inline">
          <label class="layui-form-label">物流账号</label>
          <div class="layui-input-inline layui-input-wrap">
            <select>
              <option value="">物流方式</option>
              <option value="海运">海运</option>
              <option value="空运">空运</option>
              <option value="空运">空运</option>
            </select>
          </div>
          <div class="layui-input-inline layui-input-wrap">
            <input type="text" name="vercode" autocomplete="off" lay-affix="clear"
              class="layui-input" placeholder="请填写账号">
          </div>
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-input-block">
          <input type="checkbox" name="arr[0]" title="设置为默认送货地址"><br>
          <input type="checkbox" name="arr[1]" title="发货地址与发票地址一致" checked>
        </div>
      </div>
      <div class="layui-form-item flex" style="margin-top: 40px;">
        <button class="layui-btn layui-btn-primary cancelBtn">取消</button>
        <button type="submit" class="layui-btn saveBtn" lay-submit lay-filter="demo1">保存</button>
      </div> 
    </form>

    <script>
      layui.use(['form'], function () {
        var form = layui.form;
        var layer = layui.layer;
        var verifyList = []
        var timer = 0;
        form.render();
        // 自定义验证规则
        form.verify({
          name1:(e) => { verifyList[0] = {failIndex:0,val:e,fn:hasValue}
            return false},
          name2:(e) => { verifyList[1] = {failIndex:1,val:e,fn:hasValue}
            return false},
          phone:(e) => {verifyList[2] = {failIndex:2,val:e,fn:verifyPhone}
            return false},
          addr1:(e) => {verifyList[3] = {failIndex:3,val:e,fn:hasValue}
            return false},
          addr2:(e) => {verifyList[4] = {failIndex:4,val:e,fn:hasValue}
            return false}
          // name1:(e) => verifyList.push(e),
          // name2:(e) => verifyList.push(e),
          // phone:(e) => verifyList.push(e),
          // addr1:(e) => verifyList.push(e),
          // addr2:(e) => verifyList.push(e)
        });
        /** 校验是否通过 */
        function verifyFn() {
          const textList = ['姓','名','手机号','地址','详细地址']
          const failText = layui.$('.failText');
          let isPass = true;
          verifyList.forEach((item,index) => {
            if (item.val && item.fn(item.val)) {
              console.log('验证通过',item);
              $(failText[item.failIndex]).hide(300)
            }else{
              $(failText[item.failIndex]).slideDown(300)
              console.log(textList[index] ,'-->',item);
              isPass = false
            }
          })
          closeFailText(5000)
          return isPass
        }
        /** 关闭错误提示 -防抖*/
        function closeFailText(time) {
          clearTimeout(timer)
          const failText = layui.$('.failText');
          timer = setTimeout(() => {
              verifyList.forEach((item) => {
                $(failText[item.failIndex]).hide(300)
              })
            }, time);
        }
        // 监听表单元素 是否获取到焦点
        $('form').on('focus', 'input, select, textarea', function() {
          const failTextClass = this.getAttribute('name');
          // 在这里执行你的逻辑
          if (!failTextClass && $('.'+failTextClass).length >0) return;
          const failTextDom =  $('.'+failTextClass)
          failTextDom.hide(300)
          // console.log('触发',this,failTextDom.length);
        });
        // 提交事件
        form.on('submit(demo1)', function (data) {
          if (verifyList.length ===0) return false
        
          var field = data.field; // 获取表单字段值
          // 此处可执行 Ajax 等操作
          // …

          // console.log(verifyFn());
          // 如果 验证通过了就全部关闭
          if (verifyFn()) layui.layer.closeAll();
          return false; // 阻止默认 form 跳转
        });
      });
    </script>
  </div>
</body>

</html>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <link rel="stylesheet" href="../../modules/swiper/basic/css/idangerous.swiper.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/swiper/basic/js/idangerous.swiper.min.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/payPage.css">
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
</head>


<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="header">
            <div class="logo">
                <div>
                    <img src="../../images/icons/hlk.png" class="checkOrderImage">
                </div>
                <div style="width: 1px;background-color: var(--text-color4);padding: 1.5vw 0;margin-left: 10px;"></div>
                <div style="font-size: 1.3vw;color: var(--text-color);padding-left: 20px;">结算页</div>
            </div>
            <!-- /* 步骤条 */ -->
            <div class="stepBoxContainer">
                <div class="stepBox" style="margin-bottom: 30px;">
                    <div class="item flex" style="max-width: fit-content;">
                        <div class="circle">
                            <span class="number">1</span>
                        </div>
                        <div class="stepText">查看购物车</div>
                    </div>
                    <div class="item flex">
                        <div class="stepLine"></div>
                        <div class="circle">
                            <span class="number">2</span>
                        </div>
                        <div class="stepText">核对订单信息</div>
                    </div>
                    <div class="item flex">
                        <div class="stepLine"></div>
                        <div class="circle">
                            <span class="number">3</span>
                        </div>
                        <div class="stepText">订单提交成功</div>
                    </div>
                </div>
            </div>

        </div>
        <!-- 主体 -->
        <div class="checkOrderMain">
            <div class="left">
                <!-- 收货信息部分 -->
                <div class="box">
                    <div class="flex" style="justify-content: left;">
                        <div><b>订单编号:</b> <span style="color: var(--blue-deep);">SO258103645920</span></div>
                        <div style="margin-left: 30px;"><b>支付金额:</b> <span class="red" style="font-size: 1.2vw;">¥ 5.66</span>
                        </div>
                    </div>
                    <div style="color: var(--text-color1);margin-top: 10px;">
                        为保证交期，请尽快付款，支付剩余时间: <span class="red">47:59:42 </span> 超过时限订单将自动取消
                    </div>
                </div>

                <div class="box">
                    <div style="font-size: 20px;"><b>支付方式</b></div>
                    <div class="payMethodsBox">
                        <div class="payMethod flex">
                            <div><input type="radio" name="payMethod" id="payMethod1"></div>
                            <label for="payMethod1">
                                <img src="../../images/icons/paypalzhifu.png" alt="">
                            </label>
                        </div>

                        <div class="payMethod flex">
                            <div><input type="radio" name="payMethod" id="payMethod2"></div>
                            <label for="payMethod2">
                                <img src="../../images/icons/zhifubaozhifu.png" alt="">
                            </label>
                        </div>

                        <div class="payMethod flex">
                            <div><input type="radio" name="payMethod" id="payMethod3"></div>
                            <label for="payMethod3">
                                <img src="../../images/icons/paypalzhifu.png" alt="">
                            </label>
                        </div>

                        <div class="payMethod flex">
                            <div><input type="radio" name="payMethod" id="payMethod4"></div>
                            <label for="payMethod4">
                                <img src="../../images/icons/zhifubaozhifu.png" alt="">
                            </label>
                        </div>
                        
                       
                    </div>
                </div>

            </div>
            <div class="right">
                <!-- 侧边栏 -->
                <div class="aside">
                    <div class="asideItem serverInfoIcon" style="color: black;">
                        结算明细
                    </div>
                    <div class="asideItem" style="margin-top: 10px;">
                        <div>商品总价</div>
                        <div>¥ 5.66</div>
                    </div>
                    <div class="asideItem">
                        <div>运费价格</div>
                        <div>¥ 5.66</div>
                    </div>
                    <div class="asideItem"
                        style="font-size: 16px;border-bottom: 1px solid var(--line);padding-bottom: 10px ;">
                        <div style="color: black;">合计</div>
                        <div class="red serverInfoIcon"> <b>¥ 5.66</b> </div>
                    </div>
                    <div class="asideItem borderB" style="margin:10px 0 15px 0;padding-bottom: 15px;">
                        <button class="button button_blue"
                            style="width: 95%;margin: 10px auto;border-radius: 45px;">确认支付</button>
                    </div>
                    <!--  -->
                    <div class="serverInfo">
                        <div class="serverTitle" style="font-size: 15px;">服务申明</div>
                        <div class="serverTitle flex">
                            <div class="iconfont icon-sign serverInfoIcon"
                                style="margin-left: .2vw;margin-right: .2vw;"></div>
                            <div>快递</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>支持七天无理由退货</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>如果快递丢失，支持退货</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>如果快递损坏，支持退货</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>支持90天内免费换货</div>
                        </div>

                        <div class="serverTitle"> <i class="iconfont icon-secured serverInfoIcon"></i> 安全与隐私</div>
                        <div class="serverItem" style="margin-top: 3px;">
                            <div>安全付款:未经您的同意，我们不会与任何第三方分享您的个人信息。</div>
                            <div style="margin-top: 3px;">安全的个人资料:我们保护您的隐私，确保您的个人资料安全可靠。</div>
                        </div>

                        <div class="serverTitle">
                            <i class="iconfont icon-money-circle serverInfoIcon"></i>
                            支付安全
                        </div>
                        <div class="serverItem" style="margin-top: 3px;">
                            <div class="paymentMethods flex" style="justify-content: left;margin-bottom: .5vw;">
                                <!-- <img src="../../images/icons/zhifubao.png" alt=""> -->
                                <img src="../../images/icons/paypal.png" alt="">
                                <img src="../../images/icons/weixin.png" alt="">
                            </div>
                            <div>与受欢迎的支付合作伙伴合作，您的个人信息是安全的。</div>
                        </div>
                    </div>
                    <!-- 右侧边栏 -->

                    <script>
                        /** 节流函数 */
                        function throttle(func, delay) {
                            let timer = null;
                            return function (...args) {
                                if (timer === null) {
                                    timer = setTimeout(() => {
                                        func.apply(this, args);
                                        timer = null;
                                    }, delay);
                                }
                            };
                        }
                        function isAtFooter() {
                            const footerRect = document.querySelector('.footer').getBoundingClientRect();
                            const asideRect = document.querySelector('.aside').getBoundingClientRect();
                            return asideRect.bottom > footerRect.top && asideRect.top < footerRect.bottom;
                        }
                        function handleScroll() {
                            if (isAtFooter()) {
                                $('.aside').stop(true, true).fadeOut(300);
                            } else {
                                $('.aside').stop(true, true).fadeIn();
                            }
                        }

                        // 节流处理滚动事件
                        // const throttledScroll = throttle(handleScroll, 200);
                        // window.addEventListener('scroll', throttledScroll);
                    </script>
                </div>
            </div>
        </div>
        <div class="bug"></div>
        <!-- 底部开始 -->
        <div class="footer">
            <div class="footer2">
                <div class="footer1Content">
                    <div>深圳市海凌科电子有限公司</div>
                    <div>电话 : 0755-23152658</div>
                    <div>邮箱 : <EMAIL></div>
                    <div>地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A</div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                <div class="footer1Content">
                    <div>公司</div>
                    <div>关于我们</div>
                    <div>新闻中心</div>
                    <div>品质保证</div>
                    <div>提交工单</div>
                    <div>企业社会责任</div>
                    <div></div>
                </div>
                <div class="footer1Content">
                    <div>资源</div>
                    <div>新品速递</div>
                    <div>代理招商</div>
                    <div>应用场景</div>
                    <div>服务和工具</div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                <div class="footer1Content">
                    <div>支持</div>
                    <div>联系我们</div>
                    <div>帮助</div>
                    <div>反馈</div>
                    <div>Cookie政策</div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                <!--  -->
                <div class="footer1Content">
                    <div class="connectOur">联系我们</div>
                    <div class="footerLogo">
                        <img src="../../images/icons/weixin-baidi.png" alt="" style="height: 100%;">
                        <img src="../../images/icons/youjian-baidi.png" alt="" style="height: 100%;">
                        <img src="../../images/icons/wangzhi-landi.png" alt="" style="height: 100%;">
                        <img src="../../images/icons/skype-baidi.png" alt="" style="height: 100%;">
                    </div>
                    <div><img src="../../images/icons/gzh.png" alt="" style="width: 5vw;max-width: 75px;"></div>
                    <div>关注微信订阅号</div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            <div class="footer3">
                联系电话 : 0755-23152658 版权所有 : 深圳市海凌科电子有限公司 备案号 : 粤ICP备12055399号-1 技术支持 : 极思灵创
            </div>
        </div>

        <!-- 底部结束 -->

        <script>
            function next(step = 2) {
                // 01 当前步骤
                const list = $('.item');
                list.each(function (index, item) {
                    if (index < step) {
                        item.classList.add('current')
                    }
                })
                // 02 当前步骤对应的内容
                const stepList = $('[data-step]');
                stepList.each(function (index, item) {
                    if (item.getAttribute('data-step') != step || !item.getAttribute('data-step').includes(step)) {
                        item.style.display = 'none';
                    }
                })

            }
            next(3)
        </script>
    </div>

</body>

</html>
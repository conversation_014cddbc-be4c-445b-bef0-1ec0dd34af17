/* 退款详情 */
.pageBox {
    width: 100%;
    height: 100vh !important;
    background-color: #F5F5F5;
}

.logo {
    width: 100%;
    padding: 1vw 0px;
    background-color: white;
}

.main {
    margin-left: 20%;
    padding: 1vw 0px 2vw 0px;
    width: 60%;
    /* height: 70vh; */
}

.contaniner {
    padding: 0vw 0px;
    width: 100%;
    height: 100%;
    /* border: 1px solid ; */
}
aside {
    width: 25%;
    min-height: 60vh;
    border: 1px solid var(--text-color4);
}
aside>div:not(:first-child){
    padding: 0px 1vw;
}



.asideTitle {
    padding: .5vw 1vw;
    font-size: 1.2vw;
    background-color: var(--text-color4);
}
.asideImage{
    width: 80%;
    height: 15vh;
    margin: auto;
    position: relative;
}
.asideImage>img{
    position: absolute;
    left: 0;
    top: 5%;
    width: 100%;
    height: 90%;
    object-fit: contain;
}
.asideGoodsTitle{
    text-align: center;
}
.goodsDetails{
    padding: .3vw 1vw 0vw 1vw !important;
    border-top: 1px solid var(--line);
    margin-top: 1vw;
    color: var(--text-color);
}
.goodsDetails>div{
    margin-top: .27vw;
    text-align: left;
    justify-content: left;
}
.goodsText{
    margin-left: auto;
    width: 60%;
}
.content {
    width: 75%;
    height: 60vh;
    border: 1px solid var(--line);
    display: flex;
    flex-direction: column;
}
.box{
    flex: 1;
    padding: 1vw 1vw 1vw 3vw;
    border-bottom: 1px solid var(--line);
}
.box1{
    font-size: 1.2vw;
}
.box2{
    padding-top: .5vw;
    font-size: .8vw;
    color: var(--text-color2);
}
.text2{
    margin-top: .5vw;
    padding: .5vw .5vw .5vw 1vw;
    border: 1px solid var(--line);
}
.sellerBox>div:nth-child(1){
    padding-bottom: .5vw;
    font-size: .8vw;
    font-weight: 550;
    text-indent: 0.1vw;
}
.avatarBox{
    margin-top: 2px;
    margin-right: 3px;
    width: 2vw;
    height: 2vw;
    /* margin-top: -1vw; */
    border: 1px solid var(--blue-deep);
    border-radius: 50%;
}
.avatarBox>img{
    width: 100%;
    height: 100%;
    object-fit: contain;
}
.textBox{
    margin-left: .5vw;
    color: var(--text-color1);
}
.textBox>div:nth-child(1){
    color: black;
}
.textBox>div:nth-child(2){
    margin-top: .5vw;
}
.date{
    margin-left: auto;
    margin-right: 5%;
    color: var(--text-color2);
}


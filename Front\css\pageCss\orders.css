/* 用户中心 */
.breadBox {
    border-bottom: none;
}

.main {
    margin-left: 15%;
    width: 70%;
}

.userInfoBox {
    margin-bottom: 1vw;
    /* height: 70vh; */
    display: flex;
    /* border: 1px solid ; */
}

aside {
    padding: 10px 0px;
    width: 15%;
    min-width: 200px;
    height: fit-content;
    border: 1px solid #E2E3E9;
    /* background-color: red; */
}

aside>div:nth-child(1) {
    font-size: 20px;
    padding-top: 10px;
    text-indent: 15%;
    font-weight: 550;
}

aside>div:not(:first-child) {
    margin-bottom: .5vw;
    padding: 7px 0vw;
    font-size: 15px;
    display: flex;
    justify-content: left;
    place-items: center;
    width: 100%;
    cursor: pointer;
}

aside>div>div:nth-child(1) {
    margin-right: .5vw;
    margin-left: 15%;
    font-size: 20px;
}

._line {
    width: 80% !important;
    height: 0px !important;
    border: none;
    border-bottom: 1px solid var(--line);
    margin: 0px 0px .7vw 10% !important;
}

aside>div:not(.bgSelect):hover {
    color: var(--blue-deep);
}

/* 过滤功能 */
.content {
    padding: 0px 1vw 1vw 1vw;
    /* height: 100vh; */
    /* border: 1px solid ;  */
    overflow-y: auto;
}

.filterBox {
    position: sticky;
    top: 0;
    background: white !important;
    display: flex;
    flex-wrap: wrap;
    place-items: center;
    /* border: 1px solid ;  */
    z-index: 2;
}

.filterBox>.flex {
    margin-right: 5px;
    max-width: 30%;
    min-width: 200px;
    /* border: 1px solid ; */
}

/* label */
.filterBox>div>.label {
    width: fit-content;
    white-space: nowrap;
    margin-right: .5vw;
    font-size: 14px;
}
/* 订单状态 */
.optionsBox2 {
    width: 100%;
    padding-top: 1vw;
    position: sticky;
    top: 4vh;
    background-color: white;
    /* border: 1px solid ; */
    box-sizing: border-box;
    z-index: 0;
}

.titleBox1 {
    padding-bottom: 15px;
    justify-content: left;
    font-size: 15px;
    cursor: pointer;
    /* border-bottom: 1px solid var(--line); */
}

.titleBox1>div{
    margin-left: 5%;
}

.optionsBox {
    position: sticky;
    top: 10.1vh;
    background-color: white;
    display: flex;
    place-items: center;
    z-index: 2;
    padding-bottom: 10px;
}

.optionsBox>div:not(:first-child) {
    margin-left: 15px;
}

.optionsBox>div>.button {
    background-color: white;
}


/* <!-- 所有订单-表格 --> */
.tablesBox {
    padding: 0vw 0px;
    width: 100%;
    /* height: 100vh; */
    /* border: 1px solid ; */
    max-height: 100vh;
    overflow: auto;
    box-sizing: border-box;
}

.layui-table:not(:first-child) {
    margin-top: 2vw;
}

.tableIcon {
    font-size: 2vw;
}

th {
    border: none !important;
    font-weight: 500 !important;
    font-size: .8vw !important;
}

.goodsInfo {
    padding-top: 0;
    min-width: 15vw;
    display: flex;
    /* border: 1px solid red; */
}

.goodsInfo>div:nth-child(2) {
    width: 100%;
    object-fit: fill;
    text-align: center;
}

.goodsInfo>div:nth-child(3)>div {
    color: var(--text-color2);
}

.goodsInformation>div {
    margin-top: .1vw;
    display: flex;
}

.name {
    margin-left: auto;
    width: 70%;
    font-size: 13px;
    display: block;
    color: var(--text-color);
    /* border: 1px solid red; */
}

.tdTitle {
    color: var(--text-color) !important;
    width: 90%;
    font-size: 17px;
    margin-bottom: 0.4vw;
}

td {
    vertical-align: top;
}

td>div:last-child {
    padding-bottom: .5vw;
}

.tableBtnBox {
    text-align: center;
}

.tableBtnBox>div:not(:first-child) {
    margin-top: .5vw;
}

.tableBtnBox>div>button {
    width: 80%;
    border-radius: 45px;
}

.fnTd>div:not(:first-child) {
    margin-top: .5vw;
}
/* 无数据 */
.noData {
    width: 100%;
    height: 60vh;
    text-align: center;
}

.noDataText {
    margin-top: 1vw;
    text-align: center;
    color: rgba(0, 0, 0, 0.89);
    font-size: 1.1vw;
}

.noDataBtn {
    width: 35%;
    border-radius: 45px;
    margin-top: 1vw;
    padding: .5vw 0px;
    font-size: 1vw;
}
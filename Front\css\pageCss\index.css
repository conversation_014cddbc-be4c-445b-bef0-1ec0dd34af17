 /* 首页- 主体 --------------- 开始 ------------ -----------------------------------------------------*/
 @media screen and (max-width: 1200px) {
    .mainBox0>div:not(:first-child) {
        margin-left: 5%;
        width: 90%;
    }

    .mainBox1 {
        margin-left: 5%;
        width: 90%;
    }
}

.mainBox0 {
    width: 100%;
    background-color: #EFF1F5;
    overflow-x: hidden;
    padding-bottom: 50px;
    /* border: 1px solid ; */
    /* background-color: red; */
}

.mainBox1_container {
    position: relative;
    width: 100%;
    height: 600px;
    background: var(--bg-mainBox1);
    padding-top: 30px;
}

.mainBox1 {
    position: absolute;
    padding: 40px 0px;
    width: 80%;
    height: 560px;
    min-height: 400px;
    max-height: 600px;
    margin-left: 10%;
    /* border: 1px solid ; */
    background-color: white;
    display: flex;
}

.mainBox1_left {
    width: 20%;
    position: relative;
    /* overflow: auto; */
}

.onePage {
    position: absolute;
    width: 100%;
    height: 100%;
    /* max-height: 30vw; */
    background-color: white;
    overflow-y: scroll;
    z-index: 2;
}
.onePage::-webkit-scrollbar {
    width: 10px;
}
/* 二段头部 */
.header2 {
    background-color: #f7f7f791 !important;
    color: var(--text-color);
}

/* 取消掉产品分类select的默认样式 */
.header2>.headerForm>.layui-form-select>.layui-select-title>.layui-input {
    color: var(--text-color);
}

/* header2 */
.header2_key:hover {
    color: dodgerblue;
    
}

.footer {
    background-color: #445268;
}

.footer2>div>div:nth-child(1) {
    color: white;
}

.footer1Content>div:hover {
    color: white;
}

.productCategory[data-select="true"] {
    color: var(--blue-deep);
    border-bottom: 1px solid;
}

/* z账户 */
.acountBox:hover .acountInfo{
    display: flex;
}
.acountInfo{
    position: absolute;
    top: 90%;
    width: 150%;
    padding: 1vw;
    border: 1px solid var(--line);
    box-shadow: 4px 2px 8px 0px rgba(0,0,0,0.15);
    border-radius: 24px 24px 24px 24px;
    border: 1px solid #E2E3E9;
    display: flex;
    flex-direction: column;
    background-color: white;
    z-index: 99999999999999;
    display: none;
}

.acountHead{
    /* padding: .2vw 0px 1vw 0px; */
    height: 100%;
    /* border-bottom: 1px solid var(--line); */
}
.acountHead>div:nth-child(1){
    width: 30%;
    margin-right: auto;
}
.acountHead>div:nth-child(2){
    width: 65%;
    /* border: 1px solid ; */
}
.acountHead>div>img{
    width: 100%;
}
.acountArea>div>.icon{
    margin-right: .7vw;
}
.acountArea{
    padding-bottom: 1vw;
    margin: 5px;
    border-bottom: 1px solid var(--line);
}
.acountArea>div{
    padding: .25vw 0px;
    font-size: .75vw;
    display: flex;
    justify-content: left;
    place-items: center;
}
.acountArea>div:hover{
    color: var(--blue-deep);
    cursor: pointer;
}
.loginOutBox>div{
    padding: .5vw;
    border-radius: 20px;
    cursor: pointer;
    text-align: center;
    box-sizing: border-box;
    border: 1px solid transparent;
}
/* .loginOutBox>div:hover{
    background-color: var(--blue-deep);
    border: 1px solid var(--line);
    color: white;
} */
.loginOut{
    margin-left: auto;
    color: var(--red);
    text-decoration: underline;
    font-size: .5vw;
    cursor: pointer;
    display: none;
}
.acountHead[data-login="true"]{
    display: flex;
    /* display: none; */
}
.acountHead[data-login="false"]{
    display: none;
    /* display: flex; */
}



.onePage>div {
    padding: 15px 0px;
    display: flex;
    justify-content: space-between;
    color: var(--text-color);
    cursor: pointer;
    white-space: nowrap;
    text-align: left;
    font-size: 1em;
    text-indent: 1.7cqw;
}

.hoverPages {
    position: relative;
    left: 100%;
    width: 396%;
    height: 100%;
    max-height: 40vw;
    background-color: white;
    overflow: hidden;
    display: none;
    z-index: 2;
    border-radius: 0px 25px 25px 0px;
    border: 1px solid var(--line);
    border-radius: 10px;
}

.onePage>div:hover {
    color: var(--blue-deep);
    /* background-color: var(--text-color4); */
}

.hoverPages>div>div:hover {
    color: var(--blue-deep);
    /* background-color: var(--text-color4); */
}

.onePage:hover~.hoverPages {
    display: flex !important;

}

.hoverPages:hover {
    display: flex;
}

.hoverPages>div>div {
    padding: 15px 0px;
    display: flex;
    justify-content: space-between;
    color: var(--text-color);
    cursor: pointer;
    white-space: nowrap;
    text-align: left;
    font-size: 1em;
    text-indent: 1.7cqw;
}

.twoPage {
    flex: 3;
    overflow-y: scroll;
}

.twoPage>div[data-select="true"] {
    color: var(--blue-deep);
    /* background-color: var(--text-color4); */
}

.onePage>div[data-select="true"] {
    color: var(--blue-deep);
    /* background-color: var(--text-color4); */
}
.onePage::-webkit-scrollbar {
    width: .3vw;
}
.onePage::-webkit-scrollbar-track {
    width: 6px;
    background: rgba(#101F1C, 0.1);
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
}

.onePage::-webkit-scrollbar-thumb {
    background-color: #f2f2f2;
    background-clip: padding-box;
    min-height: 28px;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
    transition: background-color .3s;
    cursor: pointer;
}

.onePage::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, .3);
}



.threePageMore {
    position: absolute;
    top: 100px;
    right: 10vw;
    font-size: .9vw !important;
}

.rightIcon {
    font-size: 20px;
    margin-left: auto;
    margin-right: 1.0em;
}

.mainBox1_center {
    display: flex;
    flex-direction: column;
    width: 80%;
    height: 100%;
}

.mainBox1_center>div:nth-child(1) {
    width: 100%;
    height: 75%;
    /* background-color: red; */
    display: flex;
}

.mainBox1_bottom {
    width: 100%;
    height: 25%;
    display: flex;
    /* background-color: green; */
}

.mainBox1_bottom>div {
    flex: 1;
    border: 1px solid var(--line);
    margin: 10px;
    border-radius: 5px;
    color: var(--text-color);
}

.mainBox1_center_left {
    width: 75%;
    height: 100%;
    overflow: hidden;
}

.mainBox1_center_right {
    width: 25%;
    height: 100%;
    /* background-color: red; */
    overflow: hidden;
}

.mainBox1_center_right>div:nth-child(1) {
    width: 100%;
    height: 25%;
    border-bottom: 1px solid var(--line);
    text-align: center;
}

.mainBox1_center_right>div:nth-child(2) {
    width: 100%;
    height: 40%;
    border-bottom: 1px solid var(--line);
}

.mainBox1_center_right>div:nth-child(3) {
    width: 100%;
    height: 35%;
}

.loginBtn {
    width: 73px;
    height: 25px;
    line-height: 0px;
    background: #E72A19;
    border-radius: 13px 13px 13px 13px;
    color: white;
    border: none;
    outline: none;
    margin-right: 20px;
}

.registerBtn {
    width: 73px;
    height: 25px;
    line-height: 0px;
    background: #2C79E8;
    border-radius: 13px 13px 13px 13px;
    color: white;
    border: none;
    outline: none;
}

.noticeBox {
    margin-top: 10px;
    margin-left: 10%;
    width: 80%;
    color: var(--text-color);
    font-size: 1em;
    /* border: 2px solid red; */
}

.noticeBox>div {
    padding: 5px 0px;
    cursor: pointer;
}
/* 轮播开始 */
.swiper-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.pagination {
    position: absolute;
    z-index: 20;
    bottom: 10px;
    width: 100%;
    text-align: center;
}

.swiper-pagination-switch {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 8px;
    background: #bbbaba;
    margin: 0 5px;
    opacity: 0.8;
    border: 1px solid #fff;
    cursor: pointer;
}

.swiper-active-switch {
    background: dodgerblue;
}

.swiper-slide>img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
/* 轮播结束 */
/* .userInfoBox2{
    border: 1px solid var(--line);
} */
/* 待支付 未发货 ... */
.userInfoBox2>div{
    /* border: 1px solid ; */
    margin: 5px auto;
    height: 40%;
    width: 80%;
}
.userInfoBox2>div:nth-child(2)>div{
    padding: 0vw .6vw .6vw .6vw;
    text-align: center;
}
.userInfoBox2>div:nth-child(2)>div:hover{
    color: var(--blue-deep);
    cursor: pointer;
}
/* icon */
.userInfoBox2>div:nth-child(2)>div>div:nth-child(1){
    font-size: 28px;
}
/* 文字 */
.userInfoBox2>div:nth-child(2)>div>div:nth-child(2){
    font-size: 12px;
    white-space: nowrap;
}


/* 猜你喜欢 */
.guessLikeBox {
    margin-top: 20px;
    width: 80%;
    margin-left: 10%;
    padding: 1vw 0px;
    background-color: white;
}

.guessLikeBox>div {
    padding-left: 1.1vw;
    font-size: 1.5vw;
}

.guessContainer {
    width: calc(100% - 20px);
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    /* border: 1px solid red !important; */
}

.guessContent {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    width: calc(100% / 7.5);
    flex-direction: column;
    background-color: white;
    margin-left: .2vw;
    margin-right: 1.3vw;
    padding: 0.5vw;
    border-radius: 10px;
}

.guessContent_desc {
    margin-top: 1vw;
    font-size: .7vw;
    font-weight: 380;
    letter-spacing: 1px;
    color: #666666;
}

.guessContent>div:nth-child(3) {
    padding: .5vw 0px;
    font-size: 0.7vw;
}

.guessContent_price {
    font-size: 1.2vw;
}
/* 新品推荐 */
.mainBox2 {
    margin-top: 60px;
    margin-left: 10%;
    width: 80%;
    padding: 1vw 0px;
    justify-content: left !important;
}

.mainBox2>div:nth-child(1) {
    background-color: #FFEADF;
    width: 48.5%;
    padding: 0.6vw 0.6vw 0.6vw 0px;
}

.mainBox2>div:nth-child(2) {
    margin-left: auto;
    background-color: #FFF4D7;
    width: 48.5%;
    padding: 0.6vw 0.6vw 0.6vw 0px;
    /* padding: 20px 0.5vw; */
}

.mainBox2_container {
    padding: 10px 0px;
    width: 100%;
    display: flex;
    justify-content: space-around;
}

.mainBox2_content {
    display: flex;
    justify-content: center;
    max-width: 20%;
    flex-direction: column;
    background-color: white;
    margin-left: 10px;
    padding: 0.5vw;
    border-radius: 10px;
    border: 1px solid rgb(230, 230, 230);
}

.mainBox2_content>div {
    padding: 0.4vw 0px 0px 0px;
}

.mainBox2_content>div:nth-child(3) {
    padding: 0px;
    font-size: 0.7vw;
}

.mainBox2_content_desc {
    font-size: .8em;
    font-weight: 380;
    letter-spacing: 1px;
    color: #666666;
}

.mainBox2_content_price {
    font-size: 1.2vw;
}


/* 排行榜 */
.rangeBox {
    width: 80%;
    margin-left: 10%;
    padding: 1vw 0px;
    background-color: white;
}

.rangeBox>div {
    padding-left: 1.1vw;
    font-size: 1.5vw;
}

.rangeFunc>div {
    padding: .5vw 1vw;
    font-size: .8vw;
    border: 1px solid;
    border-radius: 23px;
    color: var(--info-text);
    cursor: pointer;

}

.rangeFunc>div:nth-child(n+2) {
    margin-left: 1vw;
}

.rangeContainer {
    margin-top: 1vw;
    padding: .5vw .5vw;
    box-sizing: border-box;
    display: flex;
    justify-content: left;
}

.rangeContainer>div {
    margin-left: .5vw;
    margin-right: 1vw;
    flex: 1;
    background-color: #F5F5F5;
    border-radius: 18px;
    /* border: 1px solid red; */

}

.rangeContent {
    margin: 1.5vw 0vw;
    display: flex;
    justify-content: center;
    place-items: center;
    position: relative;
    /* border: 1px solid red; */
}

.rangeContent>div:nth-child(1) {
    margin-left: 1.3vw;
    padding: 1.2vw 5% 5% 5%;
    width: 40%;
    background-color: white;
    border-radius: 10px;
}

.rangeContent>div:nth-child(1)>img {
    border-radius: 15px 13px 13px 13px;
}

.rangeContent>div:nth-child(2) {
    max-width: 60%;
    /* border: 1px solid ; */
    margin-left: 2%;
    margin-right: 2%;
    font-size: .8vw;
}

.rangeContent::before {
    content: '01';
    position: absolute;
    top: 0;
    left: 1.3vw;
    width: .6vw;
    height: .8vw;
    padding: 5px;
    background-color: #9C9C9C;
    color: white;
    /* border-radius: 10px 5px 0px 0px; */
    font-size: 11px;
}

.rangeContent::after {
    content: '';
    position: absolute;
    left: 1.3vw;
    top: 1vw;
    width: 0;
    height: 0;
    border-left: .6vw solid transparent;
    border-right: .6vw solid transparent;
    border-bottom: 0.4vw solid white;
}

/* 供货商品牌开始 */
.providerBox {
    margin-top: 20px;
    width: 80%;
    margin-left: 10%;
    padding: 1vw 0px;
    background-color: white;
}

.providerBox>div {
    padding-left: 1.1vw;
    font-size: 1.5vw;
}

.providerContainer {
    width: calc(100% - 20px);
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    /* border: 1px solid red !important; */
}

.providerContent {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    place-items: center;
    width: 7%;
    height: 5vw;
    background-color: white;
    margin-right: 1.1vw;
    padding: 0.5vw;
    border-radius: 10px;
    border: 1px solid var(--line);
}
/* 侧边功能栏 */
.aside {
    width: 4%;
    height: 40%;
    background-color: white;
    position: fixed;
    top: 30%;
    right: 0%;
    border-radius: 20px 0px 0px 20px;
    padding: .2vw 0px;
    display: none;
}
.asideContainer{
    height: 100%;
    display: flex;
    flex-direction: column;
    /* border: 2px solid red; */
    user-select: none;
}

.asideContent {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    text-align: center;
    line-height: 30px;
    font-size: .7vw;
    cursor: pointer;
}

.asideContent:hover {
    color: dodgerblue;
}

.asideContent>img {
    margin: auto;
    width: 22px;
    height: 20px;
    object-fit: contain;
}
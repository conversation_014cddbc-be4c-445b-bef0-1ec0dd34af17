.pageBox {
    width: 100%;
    background-color: #F5F5F5;
}

.logo {
    width: 100%;
    padding: 1vw 0px;
    background-color: white;
}

.main {
    margin-left: 20%;
    padding: 1vw 0px 2vw 0px;
    width: 60%;
    /* height: 70vh; */
}

.contaniner {
    /* height: 60vh; */
    min-height: 600px;
    padding: 1vw;
}

.stepBox {
    width: calc(100% - 20px);
    padding: 20px 10px;
    display: flex;
}

.stepBox>.item {
    flex: 1;
    position: relative;
    /* border: 1px solid ; */
    padding: 10px 0px;
    justify-content: right;
}

.stepText {
    position: absolute;
    right: -15px;
    top: 85%;
    width: fit-content;
    white-space: nowrap;
    font-size: 16px;
}

.stepLine {
    flex: 1;
    height: 3px;
    border: 1px solid white;
    border-radius: 5px;
    transition: 1s;
    margin: 0px 10px;
    position: relative;
    background-color: var(--text-color4);
}

.circle {
    border-radius: 50%;
    width: 38px;
    height: 38px;
    margin-bottom: 5px;
    background-color: var(--text-color4);
    color: white;
    display: flex;
    justify-content: center;
    place-items: center;
}

.number {
    font-size: 16px;
}

.icon-ai210 {
    display: none;
    font-size: 20px;
}

.current>.stepLine {
    background-color: var(--blue-deep);
}

.current>.circle {
    background-color: var(--blue-deep);
    color: white;
}

.current>.stepText {
    color: var(--blue-deep);
}



/* <!-- 订单详情 --> */
.contentBox {
    margin-top: 20px;
    min-height: 300px;
    border: 1px solid var(--line);
}

.contentBox_left>div>.goodsInformation>div>.name {
    width: 77%;
    color: var(--text-color1);
}

.contentBox_left>div>.goodsInformation>div {
    margin-bottom: 12px;
    font-size: 15px;
}

/* .contentBox_left>div>.goodsInformation>div:not(:first-child){
    margin-left: 3%;
} */

.contentBox_left>div:first-child {
    font-size: 20px;
    font-weight: bold;
    padding: 10px 0px;
    text-indent: 5%;
    background-color: var(--text-color4);
}

.icon-zhuyi- {
    color: var(--blue-deep);
    font-size: 30px;
    margin-right: 20px;
}

.contentBox_right>div {
    margin-left: 5%;
}

.contentBox_right_top {
    justify-content: left;
}

.contentBox_right_center {
    margin-top: 10%;
    text-indent: 10%;
}

.actionInfo {
    font-size: 25px;
    cursor: pointer;
}

.ul {
    margin-top: 30px;
    width: 90%;
    font-size: 13px;
    /* border: 1px solid ; */
    margin-left: auto;
    color: var(--text-color2);
}

.ul>li {
    position: relative;
}

.ul>li::before {
    position: absolute;
    margin-top: 7px;
    left: -12px;
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--text-color2);
}

.contentBox_right_center_2 {
    margin: 20px 20px 20px 20px;
}

.funcBox {
    margin: 40px auto 0px 50px;
    font-size: 15px;
}

.funcBox>button {
    color: var(--blue-deep);
    padding: 5px;
    border: 1px solid;
    background: rgba(135, 207, 235, 0.158);
    font-size: 13px;
}

.funcText {
    color: var(--blue-deep);
    padding: 0px 5px;
    font-size: 13px;
    cursor: pointer;
}

/* <!-- 所有订单-表格 --> */
.tablesBox {
    margin: 20px 0px;
    width: 100%;
    overflow: auto;
    box-sizing: border-box;
    border: 1px solid var(--text-color4);
}

th {
    border: none !important;
    font-size: 14px !important;
}

.goodsInfo {
    padding-top: 0;
    min-width: 15vw;
    display: flex;
    /* border: 1px solid red; */
}

.goodsInfo>div:nth-child(2) {
    width: 100%;
    object-fit: fill;
    text-align: center;
}

.goodsInfo>div:nth-child(3)>div {
    color: var(--text-color2);
}

.goodsInformation>div {
    margin-top: .2vw;
    display: flex;
}

.name {
    margin-left: auto;
    width: 70%;
    color: var(--text-color);
    /* border: 1px solid red; */
}

.tdTitle {
    color: var(--text-color) !important;
    width: 90%;
    font-size: 17px;
    margin-bottom: 0.4vw;
}

.fnTd>div {
    margin-bottom: 10px;
}

td {
    vertical-align: top;
}

td>div:last-child {
    padding-bottom: .5vw;
}

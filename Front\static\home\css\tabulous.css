* { margin: 0; padding: 0;}
ul { list-style-type: none;}
p {
	margin-bottom: 20px;
}
.tabs{
	width: 500px;
	margin: 0 auto;
	margin-bottom: 40px;
}

.tabs li {
	float: left;
	margin-right: 2px;
}

.tabs li a {
	display: block;
	padding: 17px 30px;
	background: #f8f8f8;
	text-decoration: none;
	color: #8478B3;
}

.tabs li a:hover {
	background: #f5f5f5;
}

#tabs_container {
	padding: 40px;
	overflow: hidden;
	position: relative;
	background: #f0f0f0;
	zoom: 1;
}

#tabs_container div {
	float: left;
	margin-right: 40px;
	zoom: 1;
}

.transition {
	-webkit-transition: all .3s ease-in-out;
	-moz-transition: all .3s ease-in-out;
	-o-transition: all .3s ease-in-out;
	-ms-transition: all .3s ease-in-out;
	transition: all .3s ease-in-out;

	-webkit-transition-delay: .3s;
	-moz-transition-delay: .3s;
	-o-transition-delay: .3s;
	-ms-transition-delay: .3s;
	transition-delay: .3s;
}

.make_transist {
	-webkit-transition: all .3s ease-in-out;
	-moz-transition: all .3s ease-in-out;
	-o-transition: all .3s ease-in-out;
	-ms-transition: all .3s ease-in-out;
	transition: all .3s ease-in-out;
}

.hidescale {
	-webkit-transform: scale(0.9);
	-moz-transform: scale(0.9);
	-o-transform: scale(0.9);
	-ms-transform: scale(0.9);
	transform: scale(0.9);
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: alpha(opacity=0);
	filter: alpha(opacity=0);
	opacity: 0;
}

.showscale {
	-webkit-transform: scale(1);
	-moz-transform: scale(1);
	-o-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1);
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
	filter: alpha(opacity=100);
	opacity: 1;

	-webkit-transition-delay: .3s;
	-moz-transition-delay: .3s;
	-o-transition-delay: .3s;
	-ms-transition-delay: .3s;
	transition-delay: .3s;
}

.hideleft {
	-webkit-transform: translateX(-100%);
	-moz-transform: translateX(-100%);
	-o-transform: translateX(-100%);
	-ms-transform: translateX(-100%);
	transform: translateX(-100%);
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: alpha(opacity=0);
	opacity: 0;
}

.showleft {
	-webkit-transform: translateX(0px);
	-moz-transform: translateX(0px);
	-o-transform: translateX(0px);
	-ms-transform: translateX(0px);
	transform: translateX(0px);
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
	filter: alpha(opacity=100);
	opacity: 1;

	-webkit-transition-delay: .3s;
	-moz-transition-delay: .3s;
	-o-transition-delay: .3s;
	-ms-transition-delay: .3s;
	transition-delay: .3s;
}

.hidescaleup {
	-webkit-transform: scale(1.1);
	-moz-transform: scale(1.1);
	-o-transform: scale(1.1);
	-ms-transform: scale(1.1);
	transform: scale(1.1);
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: alpha(opacity=0);
	opacity: 0;
}

.showscaleup {
	-webkit-transform: scale(1);
	-moz-transform: scale(1);
	-o-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1);
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
	filter: alpha(opacity=100);
	opacity: 1;

	-webkit-transition-delay: .3s;
	-moz-transition-delay: .3s;
	-o-transition-delay: .3s;
	-ms-transition-delay: .3s;
	transition-delay: .3s;
}

.hideflip {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: alpha(opacity=0);
	opacity: 0;

	-webkit-transform: rotatey(-90deg) scale(1.1);
	-moz-transform: rotatey(-90deg) scale(1.1);
	-o-transform: rotatey(-90deg) scale(1.1);
	-ms-transform: rotatey(-90deg) scale(1.1);
	transform: rotatey(-90deg) scale(1.1);

	-webkit-transform-origin: 50% 50%;
	-moz-transform-origin: 50% 50%;
	-o-transform-origin: 50% 50%;
	-ms-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
}

.showflip {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
	filter: alpha(opacity=100);
	opacity: 1;

	-webkit-transition-delay: .3s;
	-moz-transition-delay: .3s;
	-o-transition-delay: .3s;
	-ms-transition-delay: .3s;
	transition-delay: .3s;

	-webkit-transform: rotatey(0deg) scale(1);
	-moz-transform: rotatey(0deg) scale(1);
	-o-transform: rotatey(0deg) scale(1);
	-ms-transform: rotatey(0deg) scale(1);
	transform: rotatey(0deg) scale(1);

	-webkit-transform-origin: 50% 50%;
	-moz-transform-origin: 50% 50%;
	-o-transform-origin: 50% 50%;
	-ms-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
}

.tabulous_active {
	background: #f0f0f0 !important;
	color: #655c89 !important;
}

.tabulousclear {
	display: block;
	clear: both;
}
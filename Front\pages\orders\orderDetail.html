<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/orderDetail.css">
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
</head>
<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="logo">
            <img src="../../images/icons/hlk.png" style="object-fit: contain;width: 137px;margin-left: 20vw;">
        </div>
        <!-- 头部结束 -->
        <div class="main">
            <!-- 面包屑 -->
            <div class="breadBox" style="border: none;">
                <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
                <div>></div>
                <div onclick="toRouter(this)" data-link="../userInfo/orders.html">订单</div>
                <div>></div>
                <div class="textSelect" onclick="toRouter(this)" data-link="../userInfo/orderDetail.html">订单详情</div>
            </div>
            <div class="contaniner">
             
                <div class="stepBox" style="margin-bottom: 30px;">
                    <div class="item flex" style="max-width: fit-content;">
                        <div class="circle">
                            <span class="number">1</span>
                            <i class="iconfont icon-ai210"></i>
                        </div>
                        <div class="stepText">买家下单</div>
                    </div>
                    <div class="item flex">
                        <div class="stepLine"></div>
                        <div class="circle">
                            <span class="number">2</span>
                            <i class="iconfont icon-ai210"></i>
                        </div>
                        <div class="stepText">买家付款</div>
                    </div>
                    <div class="item flex">
                        <div class="stepLine"></div>
                        <div class="circle">
                            <span class="number">3</span>
                            <i class="iconfont icon-ai210"></i>
                        </div>
                        <div class="stepText">卖家发货</div>
                    </div>
                    <div class="item flex">
                        <div class="stepLine"></div>
                        <div class="circle">
                            <span class="number">4</span>
                            <i class="iconfont icon-ai210"></i>
                        </div>
                        <div class="stepText">交易完成</div>
                    </div>
                </div>
               <!-- 订单详情 -->
                <div class="contentBox flex">
                    <div class="contentBox_left" style="flex: 2;border-right: 1px solid var(--line);">
                        <div>订单详情</div>
                        <div>
                            <div style="width: 90%;color: black;margin: 10px 0 20px 5%;">
                                <div style="font-size: 16px;">订单编号</div>
                                <div style="width: 77%;">
                                    <span>SO258103645920 </span>
                                    <span class="copy" onclick="copy()">复制</span>
                                </div>
                            </div>
                            <div class="goodsInformation"
                                style="width: 90%;margin:0 auto 0 5%;color: var(--text-color1);">
                                <div>收货地址:
                                    <div class="name">
                                        <i class="iconfont icon-denglu-mimakejian actionInfo"
                                            onclick="actionInfo(this)"></i>
                                    </div>
                                </div>
                                <div>收件人名称: <span class="name textOver" style="letter-spacing: 3px;"
                                        id="deliveryName">帝皇铠甲</span> </div>
                                <div class="flex">
                                    收货地址:
                                    <div class="name textOver3" id="deliveryAddr">
                                        中国 广东省 深圳市 龙华区，
                                        星河WORLD栋大厦，17层1705
                                    </div>
                                </div>
                                <div class="textOver">
                                    邮政:<span class="name textOver">15880</span>
                                </div>
                                <div class="textOver">
                                    联系电话:<span class="name">+86 <span id="phoneNumber">13712345869</span></span>
                                </div>
                                <script>
                                    /** 显示或者隐藏收获信息 */
                                    function actionInfo(e) {
                                        if (e.classList.contains('icon-denglu-mimabukejian')) { //可见
                                            e.classList.remove('icon-denglu-mimabukejian');
                                            e.classList.add('icon-denglu-mimakejian');
                                            window.location.reload()
                                        } else { //不可见
                                            e.classList.remove('icon-denglu-mimakejian');
                                            e.classList.add('icon-denglu-mimabukejian');

                                            const phoneNumber = document.getElementById('phoneNumber');
                                            phoneNumber.innerHTML = starNumber(phoneNumber.innerHTML);
                                            const deliveryAddr = document.getElementById('deliveryAddr');
                                            deliveryAddr.innerHTML = starDeliveryAddr(deliveryAddr.innerHTML);
                                            const deliveryName = document.getElementById('deliveryName');
                                            deliveryName.innerHTML = starName(deliveryName.innerHTML);
                                        }
                                    }

                                </script>
                            </div>
                        </div>
                    </div>
                    <div class="contentBox_right" style="flex: 3;min-height: 300px;">
                        <div style="padding-top: 50px;"></div>
                        <div class="contentBox_right_top flex">
                            <div class="iconfont icon-zhuyi-"></div>
                            <div style="font-size: 20px;font-weight: 550;">订单状态: <span id="orderStatus">等待商家发货</span></div>
                        </div>
                        <div class="contentBox_right_center" data-step="2">
                            您可以 <span class="copy" onclick="download('url','发票')">申请开票</span>
                        </div>
                        <div class="contentBox_right_center_2">
                            <div class="ul">
                                <li data-step="3">您还有 <span class="red">29天21小时56分45秒</span> 来确认收货，超时订单自动确认收货</li>
                                <li data-step="3" style="margin-top: 5px;">物流：UPS快递 运单号： 773318601805123</li>
                                <li  data-step="3" style="margin: 5px 0 0 40px;">2024-10-29 10:11:01 <span
                                        style="margin-left: 20px;color: var(--warning);">包裹正在等待揽收</span></li>
                                        <li  data-step="4">物流：UPS快递 运单号： 773318601805123</li>
                                        <li style="margin: 5px 0 0 40px;"  data-step="4">2024-10-29 10:11:01 <span
                                            style="margin-left: 20px;color: var(--warning);">包裹已完成取件，期待为你再次服务</span></li>
                            </div>
                            <div class="funcBox">
                                <span data-step="3,4">您可以</span>
                                <button data-step="3">确认收货</button>
                                <button onclick="toRouter(this)" data-link="../userInfo/onComment.html" data-step="4">立即评价</button>
                                <span class="funcText" data-step="3">延长收货时间</span>
                                <span class="funcText" onclick="download('url','发票')" data-step="3,4">申请开票</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 所有订单-表格 -->
                <div class="tablesBox">
                    <table class="layui-table" style="background-color: transparent;margin: 0;">
                        <colgroup>
                            <col width="40%">
                            <col width="8%">
                            <col width="8%">
                            <col width="10%">
                            <col width="10%">
                            <col width="10%">
                        </colgroup>
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th>商品信息</th>
                                <th>单价:</th>
                                <th>商品数量:</th>
                                <th>商品总额:</th>
                                <th>售后:</th>
                                <th>买家选择物流</th>
                            </tr>
                        </thead>
                      
                        <tbody>
                            <tr>
                                <td>
                                    <div class="goodsInfo" style="place-items: center;">
                                        <div class="layui-form" style="margin-right: .5vw;">
                                            <input type="checkbox" name="BBB" checked>
                                        </div>
                                        <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                            <img src="../../images/icons/device3.png" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                        </div>
                                        <div class="goodsInformation"
                                            style="width: 60%;margin-left: 5%;margin-right: auto;">
                                            <div> <span class="tdTitle textOver">220V转5V3.3V9V12V15V24V</span> </div>
                                            <div>原厂编号<span class="name textOver">ACDC电源模块</span></div>
                                            <div>制造商: <span class="name textOver">Hi-Link</span> </div>
                                            <div class="textOver">制造商编号: <span
                                                    class="name textOver">727-S40FC008C3B1V000 </span>
                                            </div>
                                            <div class="textOver">
                                                型号:<span class="name textOver">HLK-PM01</span>
                                            </div>
                                            <div class="textOver">
                                                客户编号:<span class="name">220V转5V3.3V9V12V15V24V</span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>¥ 5.66</div>
                                </td>
                                <td>
                                    <div>1</div>
                                </td>
                                <td class="tableBtnBox" rowspan="2" colspan="1">
                                    <div>合计：<span class="money">￥0</span> </div>
                                </td>
                                <td rowspan="2" colspan="1" class="fnTd">

                                </td>
                                <td rowspan="2" colspan="1" class="fnTd">
                                    <div class="hover">UPS快递</div>
                                    <div class="textSelect" onclick="toRouter(this)">¥3.68</div>
                                    <div onclick="toRouter(this)">预计到达时间: 3-7天</div>
                                </td>
                            </tr>
                            <!-- -->
                            <tr>
                                <td>
                                    <div class="goodsInfo" style="place-items: center;">
                                        <div class="layui-form" style="margin-right: .5vw;">
                                            <input type="checkbox" name="BBB" checked>
                                        </div>
                                        <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                            <img src="../../images/icons/device3.png" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                        </div>
                                        <div class="goodsInformation"
                                            style="width: 60%;margin-left: 5%;margin-right: auto;">
                                            <div> <span class="tdTitle textOver">220V转5V3.3V9V12V15V24V</span> </div>
                                            <div>原厂编号<span class="name textOver">ACDC电源模块</span></div>
                                            <div>制造商: <span class="name textOver">Hi-Link</span> </div>
                                            <div class="textOver">制造商编号: <span
                                                    class="name textOver">727-S40FC008C3B1V000 </span>
                                            </div>
                                            <div class="textOver">
                                                型号:<span class="name textOver">HLK-PM01</span>
                                            </div>
                                            <div class="textOver">
                                                客户编号:<span class="name">220V转5V3.3V9V12V15V24V</span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>¥ 5.66</div>
                                </td>
                                <td>
                                    <div>2</div>
                                </td>
                            </tr>
                            <tr>
                                <td class="hover" style="text-align: center;" colspan="4">
                                    --- 展开所有订单 ---
                                </td>
                            </tr>
                        </tbody>
                    </table>

                </div>
            </div>

        </div>
    </div>
    <div class="bug" style="background-color: #F5F5F5;"></div>
    <!-- 底部开始 -->
    <div class="footer">
        <div class="footer2">
            <div class="footer1Content">
                <div>深圳市海凌科电子有限公司</div>
                <div>电话 : 0755-23152658</div>
                <div>邮箱 : <EMAIL></div>
                <div>地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>公司</div>
                <div>关于我们</div>
                <div>新闻中心</div>
                <div>品质保证</div>
                <div>提交工单</div>
                <div>企业社会责任</div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>资源</div>
                <div>新品速递</div>
                <div>代理招商</div>
                <div>应用场景</div>
                <div>服务和工具</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>支持</div>
                <div>联系我们</div>
                <div>帮助</div>
                <div>反馈</div>
                <div>Cookie政策</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <!--  -->
            <div class="footer1Content">
                <div class="connectOur">联系我们</div>
                <div class="footerLogo">
                    <img src="../../images/icons/weixin-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/youjian-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/wangzhi-landi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/skype-baidi.png" alt="" style="height: 100%;">
                </div>
                <div><img src="../../images/icons/gzh.png" alt="" style="width: 5vw;max-width: 75px;"></div>
                <div>关注微信订阅号</div>
                <div></div>
                <div></div>
            </div>
        </div>
        <div class="footer3">
            联系电话 : 0755-23152658 版权所有 : 深圳市海凌科电子有限公司 备案号 : 粤ICP备12055399号-1 技术支持 : 极思灵创
        </div>
    </div>
    <!-- 底部结束 -->

    </div>
    <script>
        function next(step = 2) {
            // 01 当前步骤
            const list = $('.item');
            list.each(function (index, item) {
                if (index < step) {
                    item.classList.add('current')
                }
                if (index < step - 1) {
                    // 获取 .number 和 .icon-ai210 元素
                    $(item).find('.number').css("display", "none");
                    $(item).find('.icon-ai210').css("display", "block");
                }
            })
            // 02 订单状态
            if (step === 2) {
                $('#orderStatus').text('等待商家发货')
            }else if (step === 3) {
                $('#orderStatus').text('商家已发货，等待买家确认')
            }else{
                $('#orderStatus').text('交易成功')
            }
            // 03 当前步骤对应的内容
            const stepList = $('[data-step]');
            stepList.each(function (index, item) {
                if (item.getAttribute('data-step') != step || !item.getAttribute('data-step').includes(step) ) {
                    item.style.display = 'none';
                }
            })
           
        }
        next()
    </script>


</body>

</html>
/* 用户中心 */
.breadBox{
    border-bottom: none;
}
.main {
    margin-left: 15%;
    width: 70%;
}
.userInfoBox{
    margin-bottom:1vw;
    height: 70vh;
    display: flex;
    /* border: 1px solid ; */
}
aside {
    padding: 10px 0px;
    width: 15%;
    min-width: 200px;
    height: fit-content;
    border: 1px solid #E2E3E9;
    /* background-color: red; */
}

aside>div:nth-child(1) {
    font-size: 20px;
    padding-top: 10px;
    text-indent: 15%;
    font-weight: 550;
}

aside>div:not(:first-child) {
    margin-bottom: .5vw;
    padding: 7px 0vw;
    font-size: 15px;
    display: flex;
    justify-content: left;
    place-items: center;
    width: 100%;
    cursor: pointer;
}
aside>div>div:nth-child(1) {
    margin-right: .5vw;
    margin-left: 15%;
    font-size: 20px;
}
._line{
    width: 80% !important;
    height: 0px !important;
    border: none;
    border-bottom: 1px solid var(--line);
    margin: 0px 0px .7vw 10% !important;
}
aside>div:not(.bgSelect):hover{
    color:  var(--blue-deep);
}


/* content */
.content{
    width: 80%;
    height: 100%;
    overflow-y: auto;
}
.content>div{
    margin: 0vw 1vw 1.5vw 1vw;
    border-radius: 5px;
}
.box1{
    display: flex;
    flex-wrap: wrap;
    border: 1px solid var(--line);
}
.box1>.fnBox>div>div:first-child{
    font-size: 30px;

}
.box1>.title{
    padding: 1vw 0px;
    width: 100%;
    font-size: 21px;
    text-indent: 1.7vw;
    border-bottom: 1px solid var(--line);
}
.box1>div:nth-child(2){
    margin-left: 0px;
    width: 78%;
    height: 10vh;
    display: flex;
    place-items: center;
    font-size: 16px;
}
.box1>div>img{
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: contain;
}
.box1>div:last-child{
    /* margin-left: auto; */
    width: 75%;
    padding: 1vw;
    justify-content: space-evenly;
}
.box1>div:last-child>div{
    width: fit-content;
    text-align: center;
    position: relative;
    cursor: pointer;
    /* border: 1px solid ; */
}
.notices{
    position: absolute;
    left: 51%;
    top: -1vw;
    padding: .2vw .3vw;
    background-color: var(--red);
    color: white;
    z-index: 100;
    border-radius: 5px 5px 5px 0px;
}
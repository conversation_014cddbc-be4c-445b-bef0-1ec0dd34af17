.pageBox {
    width: 100%;
    background-color: #f5f5f5;
}

.header {
    width: 100%;
    background-color: white;
    display: flex;
    place-items: center;

}

.logo {
    width: fit-content;
    padding: 1vw 0px;
    background-color: white;
    display: flex;
    place-items: center;
}

.main {
    margin-left: 20%;
    padding: 1vw 0px 2vw 0px;
    width: 60%;
    /* height: 70vh; */
}

.stepBox {
    padding: 0vw 1;
}

.checkOrderImage{
    object-fit: contain;width: 137px;margin-left: 20vw;
}

/* <!-- 步骤条 --> */
.stepBoxContainer {
    margin-left: auto;
    width: 450px;
    margin-right: 20vw;
}

.stepBox {
    width: calc(100% - 20px);
    padding: 0vw 10px;
    display: flex;
}

.stepBox>.item {
    flex: 1;
    position: relative;
    /* border: 1px solid ; */
    padding: 10px 0px;
    justify-content: right;
}

.stepText {
    position: absolute;
    right: -30px;
    top: 85%;
    width: fit-content;
    white-space: nowrap;
    font-size: 16px;
}

.stepLine {
    flex: 1;
    height: 3px;
    border: 1px solid white;
    border-radius: 5px;
    transition: 1s;
    margin: 0px 10px;
    position: relative;
    background-color: var(--text-color4);
}

.circle {
    border-radius: 50%;
    width: 28px;
    height: 28px;
    margin-bottom: 5px;
    background-color: var(--text-color4);
    color: white;
    display: flex;
    justify-content: center;
    place-items: center;
}

.number {
    font-size: 20px;
}

.current>.stepLine {
    background-color: var(--hlk-red);
}

.current>.circle {
    background-color: var(--hlk-red);
    color: white;
}

.current>.stepText {
    color: var(--hlk-red);
}
/* 内容主体 */
.checkOrderMain {
    padding: 20px 0 20px 10px;
    display: flex;
    width: 60%;
    background-color: #f5f5f5;
    /* border: 1px solid ; */
    margin-left: 20%;
}

.checkOrderMain>.left {
    width: 70%;
}

.checkOrderMain>.right {
    /* margin: 0px 10px 0px 20px; */
    margin: 0 auto;
    width: 250px;
}

/* 通用样式 */
.boxTitle {
    font-size: 16px;
    color: var(--text-color);
    font-weight: bold;
    padding: 15px 20px;
}

.boxTitle2 {
    text-indent: 20px;
    font-size: 14px;
    font-weight: 550;
    position: relative;
}

/* 地址样式 */
.addressBox {
    background: white;
    border-radius: 5px;
    margin-bottom: 20px;
    min-height: 200px;
}

.addressList {
    padding: 15px 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.addressItem {
    padding: 10px 20px;
    border: 1px solid var(--line);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    transition: all 0.3s;
}

.addressItem:hover {
    border-color: var(--blue-deep);
    cursor: pointer;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.addressItem>.flex>div {
    margin-right: 30px;
}

.addressItem .address {
    color: var(--text-color);
    font-size: 15px;
    width: fit-content;
    max-width: 50%;
    line-height: 1.5;
    position: relative;
}

.addressItem .address::before {
    content: '默认地址';
    position: absolute;
    right: -6vw;
    background: var(--blue-deep);
    color: white;
    padding: 3px 15px;
    border-radius: 3px;
    font-size: 14px;
    max-width: 60px;
    display: none;
}

.currentAddr {
    border: 1px solid var(--blue-deep);
}

.currentAddr>div>.address::before {
    display: block !important;
}

/* 快递样式 */
.expressBox {
    margin-top: 20px;
    background: white;
    border-radius: 5px;
}

.expressContent {
    padding: 0 20px 20px 20px;
}

.typeTitle {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 15px;
    color: var(--text-color);
}

.typeDesc {
    color: var(--text-color2);
    font-size: 14px;
    margin-bottom: 10px;
    padding-left: 20px;
}

.expressOptions {
    padding-left: 20px;
}

.radioItem {
    display: block;
    margin-bottom: 10px;
    cursor: pointer;
    color: var(--text-color);
    font-size: 14px;
    max-width: 60%;
    min-width: 350px;
    position: relative;
}

.radioItem input[type="radio"] {
    margin-right: 10px;
    vertical-align: middle;
}

.price {
    margin-left: auto;
    color: var(--hlk-red);
    margin-left: 10px;
    font-size: 14px;
    position: absolute;
    right: 0;
}
/* 新增地址按钮 */
.addAddressBtn {
  position: absolute;
  right: 20px;
  font-size: 500 !important;
  color: var(--blue-deep);
  cursor: pointer;
}

.addr_more {
    margin-left: 20px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color3);
    cursor: pointer;
}


#expressInput {
    width: 180px;
    margin-left: 30px;
    height: 30px;
    background-color: white !important;
    border: 1px solid var(--text-color4) !important;
    border-radius: 5px;
}
/* 表格区 */
.tablesBox {
    margin: 20px 0px 0px 0px;
    padding: 10px 20px;
    width: 100%;
    overflow: auto;
    box-sizing: border-box;
    background-color: white;
    /* border: 1px solid var(--text-color4); */
}

th {
    border: none !important;
    font-size: 14px !important;
}

.goodsInfo {
    padding-top: 0;
    min-width: 15vw;
    display: flex;
    /* border: 1px solid red; */
}

.goodsInfo>div:nth-child(2) {
    width: 100%;
    object-fit: fill;
    text-align: center;
}

.goodsInfo>div:nth-child(3)>div {
    color: var(--text-color2);
}

.goodsInformation>div {
    margin-top: .2vw;
    display: flex;
}

.name {
    margin-left: auto;
    width: 70%;
    color: var(--text-color);
    /* border: 1px solid red; */
}

.tdTitle {
    color: var(--text-color) !important;
    font-size: 0.9vw;
    margin-bottom: 0.4vw;
}

.fnTd>div {
    margin-bottom: 10px;
}

td {
    vertical-align: top;
}

td>div:last-child {
    padding-bottom: .5vw;
}
/* <!-- 右侧边栏 --> */
.aside {
    width: 100%;
    /* border-radius: 10px; */
    background-color: white;
    padding: 20px;
    /* box-shadow: 0 0 4px #ccc; */
}

.asideItem {
    display: flex;
    margin-bottom: 5px;
}

.asideItem>div:first-child {
    font-size: 15px;
}

.asideItem>div:last-child {
    margin-left: auto;
}

/* 服务申明 */
.serverInfo {
    width: 100%;
    color: var(--text-color1);
}

.serverInfoIcon {
    font-size: 20px;
}

.serverTitle {
    padding: 5px 0 2px 0px;
    font-size: 13px;
    color: var(--text-color);
    justify-content: left;
}

.serverItem {
    margin-left: 1.5vw;
    justify-content: left;
    font-size: 11px;
}

.serverItem>div:last-child {
    color: var(--text-color2);
}

.serverItem>.icon-ai210 {
    color: var(--blue-deep);
}

.paymentMethods>img {
    margin-right: .2vw;
}

.coupon {
    padding: 10px 5px 0 5px;
}

.coupon>input {
    padding: 4px;
    border: 1px solid var(--line);
    color: var(--text-color1);
    letter-spacing: 2px;
}

.coupon>button {
    padding: 3px 10px;
    outline: none;
    border: none;
}

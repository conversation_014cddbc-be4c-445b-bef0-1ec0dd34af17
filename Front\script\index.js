// const CryptoJS = require("../modules/crypto");
// import CryptoJS from '../modules/crypto';
// import { showMsgDialog } from '../dialog/msgDialog.js';
//----------------------------- 公共js事件 ------------------------------------
// console.log(showMsgDialog);
var $ = layui.$;
var layer = layui.layer;
// requireJs('../modules/crypto')
/** 控制语言的切换选择 */
const showLngBoxFn = () =>{
    const showLngBoxDom = $("#showLngBox");
    const show = showLngBoxDom.attr("data-show");
    console.log(showLngBoxDom,show);
    if (show === 'true') {
        showLngBoxDom.attr("data-show","false");
        showLngBoxDom.hide(300);
    }else{
        showLngBoxDom.attr("data-show","true");
        showLngBoxDom.show(300);
        
    }

}
/** 确认设置语言 */
const confirmLngFn = () => {
    const showLngBoxDom = $("#showLngBox");
    showLngBoxDom.attr("data-show","false");
    showLngBoxDom.hide(300);
}
/** 跳转
 * @param {*} dom || url （this）一定要有data-link属性传入跳转url
 */
const toRouter = (e) => {
    if (typeof e === 'string') {
        window.location.href = e;
        return
    }
    const router = $(e).attr("data-link");
    if (!router) {
        layer.msg('该功能暂未开放', { icon: 5 });
        return
    }
    // console.log(window.location.href,router,e);
    window.location.href = router;
}
/**
 * 复制
 * @param {*} dom 需要复制内容的元素
 */
function copy(dom) {
    var text = $(dom).text();
    var input = document.createElement("input");
    document.body.appendChild(input);
    input.setAttribute("value", text);
    input.select();
    document.execCommand("Copy");
    document.body.removeChild(input);
    layer.msg('复制成功');  
}
/** 回到顶部 */
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth' // 使用平滑滚动效果
    });
}
function isSelect_productCategory(e) {
    // console.log('sds');
    // window.location.href = '../productCategory/productCategory.html';
    window.location.href = '../provider/modulesSearch.html';
    return false
    const dom =  $(e);
    const select = dom.attr('data-select');
    const value = select === 'true'?false:true;
    changeSelect($('.onePage').children()[0],true)
    // console.log(select);
    dom.attr('data-select',value);
}
function changeSelect(e,canCancel = false) {
       // window.location.href = '../productCategory/productCategory.html';
    window.location.href = '../productCategory/productCategory.html';
    return false
    const dom =  $(e);
    const select = dom.attr('data-select');
    const value = select === 'true'?false:true;
    const parentDom = e.parentNode;
    $(parentDom).children().attr('data-select', 'false')
    // 02.如果 有这个值 则可以取消为true
    if (canCancel) {
        dom.attr('data-select',value);
        const twoPageDom = $($(parentDom).siblings()[0])
        const threePageDom = $($(parentDom).siblings()[1])
        if (value) {
            twoPageDom.show()
            threePageDom.show()
        }else{
            $('.productCategory').attr('data-select','false');
            twoPageDom.hide()
            threePageDom.hide()
        }
        // console.log(e,parentDom);
        return
    }
    // 01.点击的那个永远都会是true
    dom.attr('data-select','true');
}
/**
 * 选中元素高亮（例-场景：全部-好评-差评）
 * @param {*} dom 当前点击的元素
 * @param {*} type 要选中的类型：
 * 01.onlyDataSelect:只要有data-select属性就选中
 * 02.onlyClassSelect(传进来你要设置的class):只要有class类名就选中
 * @param {*} canCancel 重复点击是否能取消-默认false
 */
function selectDiv(dom,type = 'onlyDataSelect',canCancel = false) {
    // console.log(dom,type);
    // const parentDom = dom.parentNode;
    if (type === 'onlyDataSelect') {
        const select = dom.getAttribute('data-select');
        const value = select === 'true'?false:true;
        $(dom).siblings().attr('data-select', 'false')
        if(canCancel){
            dom.setAttribute('data-select',value);
        }else{
            dom.setAttribute('data-select','true');
        }
    }else if (type != 'onlyClassSelect') { //只要不等于onlyClassSelect 都是
        const classValue = dom.getAttribute('class');
        const neverSelect = ( !classValue || !classValue.includes(type) )?true:false; 
        const select = dom.getAttribute('data-select');
        if (select) {
            const value = select === 'true'?false:true;
            $(dom).siblings().attr('data-select', 'false')
            dom.setAttribute('data-select','true');
        }
        $(dom).removeClass('titleSelect');
        $(dom).siblings().removeClass(type);
        if (canCancel) { //允许取消
            if (neverSelect) { // 没-这个class
                $(dom).addClass(type);
            }else{ //有这个class
                $(dom).removeClass(type);
            }
        }else{
            $(dom).addClass(type);
        }
        // console.log(classValue,type);
        return
    }else{
        return
    }
}

/**
 * 计算器
 * @param {*} type 1:加 0:减
 * @param {*} addCount 增量
 * @param {*} index 第几个.count元素里的和
 */
function compute(type,addCount = 1,index = 0) {
    let count = Number($($('.count')[index]).text());
    if (typeof count !== 'number') {
        layer.msg('计算错误');
        return
    }
    if (count <= 1 && type == 0) return;
    type == 1?count += addCount:count -= addCount;
    $($('.count')[index]).text(count);
}
/**
 * 直接判定登录成功（测试用例）
 */
function loginSuccess() {
    var elements = document.querySelectorAll(".isLogin[data-login='false']");
    elements.forEach(function(element) {
        element.classList.remove("isLogin");
        element.classList.add("unLogin");
    });
}
function loginOut() {
    var elements = document.querySelectorAll(".isLogin[data-login='true']");
    elements.forEach(function(element) {
        element.classList.remove("isLogin");
        element.classList.add("unLogin");
    });
}

document.addEventListener("DOMContentLoaded", function() {
    loginSuccess()
    // loginOut()
});
/**
 * 下拉收起aside菜单
 * @param {*} dom 传入要收起的元素
 */
function slide(dom) {
    // const select = dom.getAttribute('data-show');
    // const value = select === 'true'?false:true;
    // if (value) {
    //     $(dom).slideDown(1000)
    // }else{
    //     $(dom).slideUp(1000)
    // }
    // $(dom).slideDown(1000)
    $(dom).toggle(700)
    console.log(dom);
    
    
}
/**
 * 关闭遮罩
 * @param {*} isDel 是否删除后代元素，不是就写回调
 * @param {*} cb 回调函数
 */
function hideMask(isDel,cb) {
    $('#mask').fadeOut(300)
    if (isDel) {
        $(dom).children().remove()
    }
    if (cb) cb();
}
/** 下载
 * @param {*} url 下载连接
 * @param {*} name 下载名称(可选)
 */
function download(url,name) {
    const aTag = document.createElement('a')
    aTag.href = url
    if (name) {
        aTag.setAttribute('download', name);
    }
    document.body.appendChild(aTag);
    aTag.click();
    document.body.removeChild(aTag);
}
/**
 * 加载函数
 * @param {*} time 加载时间
 * @param {*} cb 回调函数
 */
function loading(time = 2000,cb) {
    var index = layer.load(0, {shade: false});
    setTimeout(function(){
        layer.close(index); // 关闭 loading
        if (cb) cb();
        
    }, time);
}
/**
 * 提示消息
 * @param {*} text 提示文字 
 * @param {*} icon 提示图标（可选）
 * @param {*} time 显示时间 2000
 */
function toast(text,icon = '',time = 2000) {
    layer.msg(text, {icon, time});
}
/**
 * 打开弹窗
 * @param {*} title 标题（温馨提示） 
 * @param {*} content 内容-您确定要执行此操作吗？ 
 * @param {*} cb 回调函数
 */
function openDialog(title = '温馨提示',content = '您确定要执行此操作吗?',cb) {
    layui.layer.open({
        title: title,
        content: content,
        area: '20vw',
        btn: ['确认', '取消'],
        yes: function(index, layero){
            layer.close(index);
            if (cb)  cb();
        },
        btn2: function(index, layero){
            layer.close(index);
        },
    })
}
 /**
  * 是否删除订单
  * @param {*} id 删除id
  * @param {*} url 删除接口url
  * @param {*} cb 回调
  */
function delOrder(id,url,cb) {
    $.post(url,{id},function(res){
        if (res.code == 200) {
            layer.msg(res.msg);
            if (cb) cb();
        }
    })
}

/** 
 * 添加文件
 *  */
async function addFile() {
    return await new Promise((resolve,reject)=>{
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.onchange = function () {
            const file = fileInput.files[0];
            resolve(file)
        }
        fileInput.click()
        //监听选择文件后删除该input
        fileInput.remove()
    })
}

/** 让textarea自动滚到可视范围 */
function textareaOninput(e) {
    e.scrollTop = e.scrollHeight;
    e.parentElement.setAttribute('data-currentCount', e.value.length)
}

 /** 是否不为空值 */
 function hasValue(e) {
    e = e?true:false
    return e;
}
/** 校验全球的手机号*/
function verifyPhone(e) {
    const pattern = /^(?:\+?86)?1(?:3\d{3}|5[0-35-9]\d{2}|8[0-9]\d{2}|7[06-8]\d{2}|4(?:0[0-8]|1[0-2]|4[0-7]|5[0-6]|7[0-8]|9[0-5])\d{3}|6[567]\d{4}|9[189]\d{3}|66\d{8})$/;
    return pattern.test(e);
}

async function getCode(dom) {
    const btn = $(dom)
    const setp = btn.attr('data-codeStep')
    console.log(btn,setp);
    if(setp == 1 || setp == 4){
        // await sendSms({Mobile:list.value[0].value,SmsType:'findphone'})
        // .then(res=>{
        //     // console.log('短信',res);
        //     if(res.data.Code!=1){
        //         ref_toast.value?.info(res.data.Message)
        //         return
        //     }
            layui.layer.msg('发送成功')
            btn.text('已发送')
            btn.attr('data-codeStep',2)
            timerFn(btn,60)	
        // })
    }else{
        layui.layer.msg('请勿重复获取')
    }
}
function timerFn(btn,time) {
    let timer = setInterval(()=>{
        time -= 1
        btn.text('已发送：' + time)
        btn.attr('data-codeStep',3)
        if(time <= 0){
            clearInterval(timer)
            btn.text('重新发送')
            btn.attr('data-codeStep',4)
        }
    },1000)
}
/** 检查邮箱 */
function checkEmail(inputDom) {
    const val = $(inputDom).val();
    const reg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    if (reg.test(val)) {
        $('.failText').css('display', 'none');
    }  else{
        $('.failText').css('display', 'block');
    }
}
/** 检查手机号 */
function checkPhone(inputDom) {
    const val = $(inputDom).val();
    const reg = /^(?:\+?86)?1(?:3\d{3}|5[0-35-9]\d{2}|8[0-9]\d{2}|7[06-8]\d{2}|4(?:0[0-8]|1[0-2]|4[0-7]|5[0-6]|7[0-8]|9[0-5])\d{3}|6[567]\d{4}|9[189]\d{3}|66\d{8})$/;
    if (reg.test(val)) {
        $('#failPhone').css('display', 'none');
    }  else{
        $('#failPhone').css('display', 'block');
    }
}
/** 隐藏密码 */
function starNumber(inputString) {
    const maskedString = inputString.slice(0, 3) + ' **** ' + inputString.slice(8);
    return maskedString;
}
/** 隐藏收获地址 */
function starDeliveryAddr(inputString) {
    var list = []
    list = inputString.split('，').length < 2 ? inputString.split(',') : inputString.split('，')
    if (inputString.length-1 >=10) {
        console.log(list);
        return ' *** *** *** ' + (list[list.length-2] + list[list.length-1])
    }
    return inputString + '*'.repeat(inputString.length-1)
}
/** 隐藏名字 */
function starName(inputString) {
   let inputString1= inputString[0]
   return inputString1 + '*'.repeat(inputString.length-1)
}
var scrollDistance = 0;
/**
 * 完美滚动函数
 * @param {*} chi_id:要滚动的item元素的id，f_id：可滚动的父元素id ->或jq元素 
 * @returns 
 */
function brand_scroll(chi_id = '',f_id = '') {
    // console.log('走起',!chi_id,!f_id);
    if(!chi_id || !f_id) return;
    var fdiv = f_id
    var cdiv = chi_id
    if ( typeof chi_id === "string") {
        cdiv = $('#'+ chi_id)
    }
    if (typeof f_id === 'string') {
        fdiv = $('#' + f_id)
    }
    if (cdiv.length === 0) return
    fdiv.scrollTop(-scrollDistance);
    var sp_top = cdiv.offset().top;
    var div_top = fdiv.offset().top;
    fdiv.scrollTop(sp_top - div_top);
    scrollDistance = sp_top - div_top;

}
/** 节流函数 */
function throttle(func, delay) {
    let timer = null;
    return function(...args) {
        if (timer === null) {
        timer = setTimeout(() => {
            func.apply(this, args);
            timer = null;
        }, delay);
        }
    };
}
/**
 * 
 * @returns 是否在顶部
 */
function isAtTop() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const clientHeight = document.documentElement.clientHeight;
    return scrollTop === 0 || scrollTop < clientHeight;
}
/**
 * 
 * @returns 是否在底部
 */
function isAtBottom() {
    const documentHeight = document.documentElement.scrollHeight;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    return scrollTop + document.documentElement.clientHeight >= documentHeight
}
// /** 计算hash  */
// const generateHash =  function (str) {
//         const hash = CryptoJS.MD5(str).toString();
//         console.log(hash);
//         return hash;
// };
// function loginOut() {
//     var elements = document.querySelectorAll(".isLogin[data-login='true']");
//     elements.forEach(function(element) {
//         element.classList.remove("isLogin");
//         element.classList.add("unLogin");
//     });
// }

// 监听全局的点击事件
// $(document).on('click', function (e) {
//     if (!$(e.target).closest('#showLngBoxDom').length) {
//         showLngBoxDom.hide();
//     }
// });
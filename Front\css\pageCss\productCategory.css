/* 产品模块分类页面样式 */
.main{
    margin-left: 15%;
    width: 70%;
}
.title{
    padding: 1vw;
    font-size: 17px;
    background-color: var(--grey-bg);
    font-weight: 600;
}
.contentBox{
    margin-top: 1vw;
    padding: 1vw 0px;
    display: flex;
}
aside{
    width: 14%;
    min-width: 160px;
    height: fit-content;
    border: 1px solid var(--lineLight);
}
aside>div{
    cursor: pointer;
    padding: 15px 1vw;
    font-size: 1em;
}
.container{
    width: 85%;
}
.searchInputBox{
    width: 30%;
    margin-left: auto;
    position: relative;
    margin-right: 10%;
    background-color: rgb(247, 247, 247);
    font-weight: 500; 
    display: flex;
    place-items: center;
}
.search_input{
    text-indent: 20px;
    width: 100%;
    padding: 0px 10px;
    font-size: 13px;
}
.inputIcon{
    position: absolute;
    margin-top: -11px;
    font-size: 1.2em !important;
    padding-left: 5px;
    color: var(--text-color2);
}
.content{
   padding: 0px 1vw 1vw 1vw;
   margin-left: 2vw;
   margin-bottom: 10vw;
}
.logoBox{
    margin-top: 2vw;
    padding: 2vw 0px;
}
.productBox{
    padding: 1.2vw 0px;
    border-bottom: 1px solid var(--info);
}
.productTitle{
    font-size: 17px;
    color: black;
    font-weight: 500;
    cursor: pointer;
}
.productList{
    display: flex;
    flex-wrap: wrap;
}
.introduceItem{
    margin-left: 1%;
    padding: 5px 15px ;
    height: 70px;
    display: flex;
    flex-wrap: wrap;
    place-items: center;
    position: relative;
    box-sizing: border-box;
    color: var(--blue);
    cursor: pointer;
    font-size: 15px;
    text-align: center;
    /* border: 1px solid ; */
}
.introduceItem::before{
    content: '';
    position: absolute;
    top: calc(50% - 3px);
    left: 0px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--blue);
}
.productList>div>span{
    color: var(--text-color3);
}
.introduceItem{
    color: var(--blue);
}

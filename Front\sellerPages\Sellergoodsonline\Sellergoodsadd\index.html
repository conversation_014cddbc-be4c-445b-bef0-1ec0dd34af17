
<!doctype html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>商家中心</title>
        <meta name="renderer" content="webkit|ie-comp|ie-stand">
        <meta name="keywords" content="" />
        <meta name="description" content="" />
        <link rel="stylesheet" href="/static/home/<USER>/common.css">
        <link rel="stylesheet" href="/static/home/<USER>/seller.css">
        <link rel="stylesheet" href="/static/plugins/js/jquery-ui/jquery-ui.min.css">
        <script>
            var BASESITEROOT = "";
            var HOMESITEROOT = "/static/home";
            var BASESITEURL = "http://b2b2c.h.com/index.php";
            var HOMESITEURL = "http://b2b2c.h.com/index.php/home";
        </script>
        <script src="/static/plugins/jquery-2.1.4.min.js"></script>
        <script src="/static/plugins/js/jquery-ui/jquery-ui.min.js"></script>
        <script src="/static/plugins/js/jquery-ui/jquery.ui.datepicker-zh-CN.js"></script>
        <script src="/static/plugins/common.js"></script>
        <script src="/static/plugins/jquery.validate.min.js"></script>
        <script src="/static/plugins/additional-methods.min.js"></script>
        <script src="/static/plugins/layer/layer.js"></script>
        <script src="/static/home/<USER>/member.js"></script>
        <script src="/static/plugins/js/dialog/dialog.js" id="dialog_js" charset="utf-8"></script>
        <script>
            jQuery.browser={};(function(){jQuery.browser.msie=false; jQuery.browser.version=0;if(navigator.userAgent.match(/MSIE ([0-9]+)./)){ jQuery.browser.msie=true;jQuery.browser.version=RegExp.$1;}})();
        </script>
    </head>
    <body>
        <div id="append_parent"></div>
        <div id="ajaxwaitid"></div>
        


<div class="seller_main">
    <div class="seller_left">
    <div class="seller_left_1">
        <div class="logo">
            <a href="/index.php/home/<USER>/index.html">
                <img src="http://b2b2c.h.com/uploads/home/<USER>/seller_center_logo.jpg"/>
            </a>
        </div>
        <div class="sidebar">
            <a href="/index.php/home/<USER>/index.html?store_id=1" target="_blank"><i class="iconfont">&#xe6da;</i>店铺</a>
                        <a href="../goods/publish.html" class="active"><i class="iconfont">&#xe732;</i>商品</a>
                        <a href="/index.php/home/<USER>/index.html" ><i class="iconfont">&#xe71f;</i>订单</a>
                        <a href="/index.php/home/<USER>/index.html" ><i class="iconfont">&#xe704;</i>促销</a>
                        <a href="../home/<USER>" ><i class="iconfont">&#xe663;</i>店铺</a>
                        <a href="/index.php/home/<USER>/index.html" ><i class="iconfont">&#xe6ab;</i>售后</a>
                        <a href="/index.php/home/<USER>/index.html" ><i class="iconfont">&#xe6a3;</i>统计</a>
                        <a href="/index.php/home/<USER>/index.html" ><i class="iconfont">&#xe61c;</i>客服</a>
                        <a href="/index.php/home/<USER>/account_list.html" ><i class="iconfont">&#xe702;</i>账号</a>
                        <a href="/index.php/home/<USER>/goods_list.html" ><i class="iconfont">&#xe6ed;</i>分销</a>
                    </div>
        <div class="mb">
            <a href="../login/sellerLogin.html">退出</a>
        </div>
    </div>
    <div class="seller_left_2">
        <div class="mt">
            商品                                                                                                                    </div>
        <div class="mc">
                        <a href="./publish.html" class="active">商品发布</a>
                        <a href="./goodsCSV.html">商品CSV导入</a>
                        <a href="/index.php/home/<USER>/index.html" >出售中的商品</a>
                        <a href="/index.php/home/<USER>/index.html" >仓库中的商品</a>
                        <a href="/index.php/home/<USER>/index.html" >关联版式</a>
                        <a href="/index.php/home/<USER>/index.html" >商品规格</a>
                        <a href="/index.php/home/<USER>/index.html" >图片空间</a>
                        <a href="/index.php/home/<USER>/index.html" >视频库</a>
                        <a href="/index.php/home/<USER>/index.html" >店铺服务</a>
                                                                                                                                            </div>
    </div>
</div>
    <div class="seller_right">
        <div class="seller_items">
                    </div>
                <ul class="add-goods-step">
            <li class="current"><i class="icon iconfont">&#xe600;</i>
                <h6>STEP.1</h6>
                <h2>选择商品分类</h2>
                <i class="arrow iconfont">&#xe687;</i> </li>
            <li><i class="icon iconfont">&#xe731;</i>
                <h6>STEP.2</h6>
                <h2>填写商品详情</h2>
                <i class="arrow iconfont">&#xe687;</i> </li>
            <li><i class="icon iconfont">&#xe6a2;</i>
                <h6>STEP.3</h6>
                <h2>上传商品图片</h2>
                <i class="arrow iconfont">&#xe687;</i> </li>
            <li><i class="icon iconfont">&#xe64d;</i>
                <h6>STEP.4</h6>
                <h2>商品发布成功</h2>
            </li>
        </ul>
        <!--S 分类选择区域-->
<div class="wrapper_search p20">
  <div class="wp_sort">
    <div id="dataLoading" class="wp_data_loading">
      <div class="data_loading">加载中...</div>
    </div>
    <div class="sort_selector">
      <div class="sort_title">您常用的商品分类：        <div class="text" id="commSelect">
            <div>请选择</div>
            <div class="select_list" id="commListArea">
                <ul>
                                        <li id="select_list_no" ><span class="title">您还没有添加过常用的分类</span></li>
                </ul>
            </div>
        </div>
        <i class="iconfont">&#xe689;</i>
      </div>
    </div>
    <div id="class_div" class="wp_sort_block">
      <div class="sort_list">
        <div class="wp_category_list">
          <div id="class_div_1" class="category_list">
              <ul>
                                    <li class="" dstype="selClass" data-param="{gcid:1,deep:1,tid:16}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i>手机/ 运营商/ 智能数码</a></li>
                                    <li class="" dstype="selClass" data-param="{gcid:4,deep:1,tid:1}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i> 电视/ 空调/ 冰箱/ 洗衣机</a></li>
                                    <li class="" dstype="selClass" data-param="{gcid:8,deep:1,tid:1}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i> 厨卫电器/ 生活家电/ 厨具</a></li>
                                    <li class="" dstype="selClass" data-param="{gcid:9,deep:1,tid:1}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i> 电脑办公/ 相机/ DIY</a></li>
                                    <li class="" dstype="selClass" data-param="{gcid:10,deep:1,tid:1}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i> 家居/ 家具/ 家装</a></li>
                                    <li class="" dstype="selClass" data-param="{gcid:11,deep:1,tid:1}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i> 食品/ 酒水/ 生鲜/ 特产</a></li>
                                    <li class="" dstype="selClass" data-param="{gcid:12,deep:1,tid:1}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i>个护化妆/ 纸品清洁/ 宠物</a></li>
                                    <li class="" dstype="selClass" data-param="{gcid:15,deep:1,tid:1}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i> 男装/ 女装/ 内衣</a></li>
                                    <li class="" dstype="selClass" data-param="{gcid:16,deep:1,tid:1}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i> 鞋靴/ 箱包/ 钟表/ 珠宝</a></li>
                                    <li class="" dstype="selClass" data-param="{gcid:468,deep:1,tid:0}"> <a class="" href="javascript:void(0)"><i class="iconfont"></i>母婴/玩具/车床/童装</a></li>
                                                  </ul>
          </div>
        </div>
      </div>
      <div class="sort_list">
        <div class="wp_category_list blank">
          <div id="class_div_2" class="category_list">
            <ul>
            </ul>
          </div>
        </div>
      </div>
      <div class="sort_list sort_list_last">
        <div class="wp_category_list blank">
          <div id="class_div_3" class="category_list">
            <ul>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="alert">
    <dl class="hover_tips_cont">
      <dt id="commodityspan"><span style="color:#F00;">请选择商品类别</span></dt>
      <dt id="commoditydt" style="display: none;" class="current_sort">您当前选择的商品类别是：</dt>
      <dd id="commoditydd"></dd>
    </dl>
  </div>
  <div class="wp_confirm">
      <form method="get" action="/index.php/home/<USER>/add_step_two.html">
          <input type="hidden" name="commonid" id="commonid" value="" />
          <input type="hidden" name="class_id" id="class_id" value="" />
          <input type="hidden" name="t_id" id="t_id" value="" />
          <div class="bottom tc">
              <input disabled="disabled" dstype="buttonNextStep" value="下一步，填写商品信息" type="submit" class="submit" />
          </div>
      </form>
  </div>
</div>
<script src="/static/plugins/mlselection.js"></script>
<script src="/static/plugins/jquery.mousewheel.js"></script>
<script src="/static/home/<USER>/sellergoods_add_step1.js"></script> 
<script>
SEARCHKEY = '请输入商品名称或分类属性名称';
</script>

        
        
    </div>
</div>
<script src="/static/plugins/jquery.cookie.js"></script>
<script src="/static/home/<USER>/compare.js"></script>
<link rel="stylesheet" href="/static/plugins/perfect-scrollbar.min.css">
<script src="/static/plugins/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/plugins/js/qtip/jquery.qtip.min.js"></script>
<link href="/static/plugins/js/qtip/jquery.qtip.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/plugins/jquery.lazyload.min.js"></script>
<script>
    //懒加载
    $("img.lazyload").lazyload({
//        placeholder : "/static/home/<USER>/loading.gif",
        effect: "fadeIn",
        skip_invisible : false,
        threshold : 200,
    });
</script>
<div class="footer-info">
    <div class="links w1200">
                <a href="http://www.csdeshang.com/" target="_blank">关于我们</a>|
                <a href="http://www.csdeshang.com/" target="_blank">联系我们</a>|
                <a href="http://www.csdeshang.com/" target="_blank">友情链接</a>|
                <a href="http://www.csdeshang.com/" target="_blank">商城公益</a>|
                <a href="http://www.csdeshang.com/" target="_blank">商城社区</a>|
                <a href="http://www.csdeshang.com/" target="_blank">营销中心</a>|
            </div>
    <div class="copyright">
        <p><a href="http://www.beian.gov.cn/portal/registerSystemInfo" target="_blank"></a></p>
        <p><a href="https://beian.miit.gov.cn" target="_blank">2</a></p>
        <p>Copyright &copy; 2013-2063 德尚网络开源系统 版权所有 保留一切权利</p>
    </div>
</div>

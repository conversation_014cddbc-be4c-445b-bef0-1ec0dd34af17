/* 某个供应商详情页面的css样式 */
.main{
    margin-left: 15%;
    width: 70%;
}
.breadBox{
    background-color: white;
}
.content{
    margin-left: 7%;
    width: 86%;
}
.logoBox{
    margin-top: 1vw;
    padding: 2vw 0px;
}
.productBox{
    padding: 1.2vw 0px;
    /* border-bottom: 1px solid var(--info); */
    border-bottom: 1px solid var(--line);
}
.productTitle{
    font-size: 17px;
    color: black;
    font-weight: 500;
    cursor: pointer;
}
.productList{
    margin-left: 2%;
    display: flex;
    /* justify-content: space-around; */
    flex-wrap: wrap;
}
.introduceItem{
    margin-left: 1%;
    padding: 5px 15px ;
    height: 70px;
    display: flex;
    flex-wrap: wrap;
    place-items: center;
    position: relative;
    box-sizing: border-box;
    color: var(--blue);
    cursor: pointer;
    font-size: 15px;
    text-align: center;
    /* border: 1px solid ; */
}
.introduceItem::before{
    content: '';
    position: absolute;
    top: calc(50% - 3px);
    left: 0px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--blue);
}
.count{
    color: var(--text-color3);
}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <link rel="stylesheet" href="../../modules/swiper/basic/css/idangerous.swiper.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/cart.css">
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
</head>

<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="header">
            <div class="headerItem" style="width: 10vw;">
                <img src="../../images/icons/hlk.png" style="object-fit: contain;width: 137px;">
            </div>
            <div class="headerItem" style="width: 35%;">
                <div class="searchBox flex">
                    <span class="layui-form headerForm">
                        <select class="select" name="select" id="" style="font-size: 1em;" title="分类">
                            <option value="1">商家</option>
                            <option value="2">品牌</option>
                            <option value="3">型号</option>
                            <option value="4">产品规格</option>
                        </select>
                    </span>
                    <span class="line"></span>
                    <input class="input searchInput" type="text" placeholder="请输入产品规格/型号/品牌/商家">
                    <button class="button searchButton flex" style="font-size: .9vw;">
                        <div class="iconfont icon-search" style="margin-right: 5px;font-size: 1vw;"></div>
                        搜索
                    </button>
                    <!-- 推荐关键字 -->
                    <div class="header_keyBox flex">
                        <input class="header_keyCheck" type="radio" id="option1" name="menuOptions" checked>
                        <label class="header_key textOver" for="option1">菜单1</label>

                        <input class="header_keyCheck" type="radio" id="option2" name="menuOptions">
                        <label class="header_key textOver" for="option2">菜单2</label>

                        <input class="header_keyCheck" type="radio" id="option3" name="menuOptions">
                        <label class="header_key textOver" for="option3">菜单3</label>
                    </div>
                </div>
            </div>
            <div class="headerItem" style="font-size: .9vw;min-width: 100px;width: 10%;">
                <div class="flex textOver">
                    <input type="checkbox" name="" id="goods1">
                    <label for="goods1" style="margin: -4px 5px;">库存量</label>
                </div>
                <div class="flex textOver" style="margin-left: auto;">
                    <input type="checkbox" name="" id="goods2">
                    <label for="goods2" style="margin: -4px 5px;">PHUJ</label>
                </div>
            </div>
            <div class="headerItem" style="color: var(--text-color);font-size: 1.1em;margin-left: 7%;">
                <i class="iconfont icon-weidenglu icon" style="font-size: 1.4em;margin-right: 12px;"></i>
                <span class="hoverText textOver" onclick="toRouter(this)" data-link="../login/login.html">登录</span>/
                <span class="hoverText textOver" onclick="toRouter(this)" data-link="../login/register.html">注册</span>
            </div>
            <div class="headerItem" style="color: var(--text-color);font-size: 1.1em;">
                <i class="iconfont icon-cart icon" style="font-size: 1.4em;margin-right: .2em;"></i>
                <span class="hoverText textOver">购物车</span>
            </div>
        </div>
        <!-- 头部分类 -->
        <div class="header2 textOver">
            <div class="flex productCategory" data-select="false" onclick="isSelect_productCategory(this)"
                style="margin-left: 10%;user-select: none;cursor: pointer;">
                <label for="select" class="iconfont icon-fenlei2" style="font-size: 26px;">
                </label> 产品分类 <i class="iconfont icon-xiangxia"></i>

                <div class="productCategoryBox">
                    <!-- 一级页面 -->
                    <div class="productCategory_onePage">
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> ACDC电源模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> DCDCD电源模组</span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 雷达模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> WIFI模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> BLE模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 人脸识别 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    </div>
                    <script>
                        // 监听鼠标hover事件
                        var productCategoryBox_dom = $(".productCategoryBox");
                        productCategoryBox_dom.mouseover(function (e) {
                            if (e.target.getAttribute("data-select")) {
                                $(e.target).siblings().attr("data-select", "false");
                                e.target.setAttribute("data-select", "true");
                            }
                        })
                    </script>

                    <div class="productCategory_hoverPages">

                        <div class="twoPage">
                            <div data-select="true" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> ACDC电源模组 </span>
                            </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> DCDCD电源模组</span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 雷达模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> WIFI模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> BLE模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 人脸识别 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 指纹模组 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 机壳电源 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模组 </span>
                            </div>
                        </div>

                        <div class="threePage">
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                        </div>
                        <div class="threePageMore">更多</div>
                    </div>
                    <!--  -->
                </div>
            </div>
            <span class="line2" style="height: 17px;margin-left: 10px;"></span>
            <div onclick="toRouter(this)" data-link="../index/index.html">
                <label class="header2_key">首页</label>
            </div>
            <!-- 第一版 -先干到海凌科品牌商去 -->
            <div onclick="toRouter(this)" data-link="../provider/providerDetail.html">
                <!-- <div onclick="toRouter(this)" data-link="../provider/provider.html"> -->
                <label class="header2_key">供应商</label>
            </div>
            <div>
                <label class="header2_key">Bom Tool</label>
            </div>
            <div>
                <label class="header2_key">RFQ</label>
            </div>
            <div>
                <label class="header2_key">技术资源</label>
            </div>
            <div>
                <label class="header2_key">帮助中心</label>
            </div>

            <div class="flex" style="margin-left: auto;margin-right: 10%;position: relative;">
                更改语言：<span class="lngText" onclick="showLngBoxFn()">中文</span>
                <div style="margin-left: 2vw;font-size: 13px;">
                    货币：
                    <img src="../../images/icons/zhongguo.png" style="width: 1.5vw;object-fit: contain;">
                    <span class="lngText" onclick="showLngBoxFn()"
                        style="color: white;text-decoration: underline;">RMB￥</span>
                </div>
                <!-- 语言切换 & 货币切换盒子 -->
                <div class="showLngBox" id="showLngBox" data-show="false" style="display: none;">
                    <div>更改语言</div>
                    <div class="layui-form">
                        <select class="layui-select">
                            <option value="0">选择语言</option>
                        </select>
                    </div>
                    <div>货币</div>
                    <div style="position: relative;" class="layui-form">
                        <select class="layui-select">
                            <option class="option1">请选择</option>
                        </select>
                    </div>
                    <div style="margin-top: .5vw;">
                        <button class="layui-btn" onclick="confirmLngFn()"
                            style="background-color: var(--blue-deep);border-radius: 30px !important;">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 头部结束 -->
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
            <div>></div>
            <div onclick="toRouter(this)" data-link="../userInfo/accountInfo.html">账号中心</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="../userInfo/cart.html">购物车</div>
        </div>
        <!-- 用户中心 -->
        <div class="userInfoBox">
           
            <aside>
                <div>账户中心</div><div class="_line"></div>
                <div onclick="toRouter(this)" data-link="../userInfo/accountInfo.html">
                    <div class="iconfont icon-weidenglu"></div>
                    <div>账号信息</div>
                </div>
                <div onclick="toRouter(this)" data-link="../userInfo/orders.html">
                    <div class="iconfont icon-a-description2x"></div>
                    <div>订单</div>
                </div>
                <div onclick="toRouter(this)" data-link="../userInfo/refund.html">
                    <div class="iconfont icon-wuliu"></div>
                    <div>退货和退款</div>
                </div>
                <div class="bgSelect" onclick="toRouter(this)" data-link="../userInfo/cart.html">
                    <div class="iconfont icon-a-cartline"></div>
                    <div>购物车</div>
                </div>
                <div onclick="toRouter(this)" data-link="../userInfo/wish.html">
                    <div class="iconfont icon-heart"></div>
                    <div>心愿清单</div>
                </div>
                <div class="_line"></div>
                <div onclick="toRouter(this)" data-link="../userInfo/message.html">
                    <div class="iconfont icon-xiaoxitongzhi"></div>
                    <div>信息中心</div>
                </div>
                <div onclick="toRouter(this)" data-link="../userInfo/comments.html">
                    <div class="iconfont icon-edit"></div>
                    <div>评价</div>
                </div>
                <div onclick="toRouter(this)" data-link="../userInfo/invoice.html">
                    <div class="iconfont icon-wuliuxinxi"></div>
                    <div>发票</div>
                </div>
                <div class="_line"></div>
                <div onclick="toRouter(this)" data-link="../userInfo/accountSettings.html">
                    <div class="iconfont icon-shezhi2"></div>
                    <div>账户设置</div>
                </div>
                <div onclick="toRouter(this)" data-link="../userInfo/connection.html">
                    <div class="iconfont icon-dianhua"></div>
                    <div>联系方式</div>
                </div>
                <div onclick="toRouter(this)" data-link="../userInfo/payment.html">
                    <div class="iconfont icon-creditcard"></div>
                    <div>支付方式</div>
                </div>
            </aside>
            <div class="content" data-show="true" style="display: block;padding: 0vw 1vw 1vw 1vw;">
                <!-- 所有订单-表格 -->
                <div class="tablesBox">
                    <table class="layui-table" lay-skin="line" style="background-color: white;margin: 0px;">
                        <colgroup>
                            <col width="50%">
                            <!-- <col width="20%">
                            <col width="5%"> -->
                        </colgroup>
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th>
                                    <div class="layui-form flex" style="margin-right: .5vw;place-items: center;justify-content: left;">
                                        <input type="checkbox" name="BBB" checked>海凌科供应商 
                                        <i class="iconfont icon icon-zaixiankefu1" style="margin:0px 0 0 .5vw;"  onclick="toRouter(this)" data-link="../userInfo/message.html"></i>
                                    </div>
                                </th>
                                <th></th>
                                <th class="tableIcon flex" style="margin-right: auto;justify-content: right;">
                                    <i class="iconfont icon icon-aixin2" style="margin-right: 1.5vw;"></i>
                                    <i class="iconfont icon icon-icdelete"></i>
                                </th>
                            </tr>
                        </thead>
                      
                        <tbody>
                            <tr>
                                <td>
                                    <div class="goodsInfo" style="place-items: center;">
                                        <div class="layui-form" style="margin-right: .5vw;">
                                            <input type="checkbox" name="BBB" checked>
                                        </div>
                                        <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                            <img src="../../images/icons/device3.png" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                        </div>
                                        <div class="goodsInformation"
                                            style="width: 60%;margin-left: 5%;margin-right: auto;">
                                            <div class="tdTitle flex"> 
                                                <div style="width: 60%;" class="textOver">220V转5V3.3V9V12V15V24V</div> 
                                                <div class="textSelect pointer" style="margin-left: auto;font-size: .7vw;"
                                                onclick="toRouter(this)" data-link="../product/productDetails.html">商品详情></div>
                                            </div>
                                            <div>原厂编号<span class="name textOver">ACDC电源模块</span></div>
                                            <div>制造商: <span class="name textOver">Hi-Link</span> </div>
                                            <div class="textOver">制造商编号: <span
                                                    class="name textOver">727-S40FC008C3B1V000 </span>
                                            </div>
                                            <div class="textOver">
                                                型号:<span class="name textOver">HLK-PM01</span>
                                            </div>
                                            <div class="textOver flex">
                                                <div> 客户编号:</div>
                                                <div class="flex" style="margin: auto;width: 60%;height: 1.9vw;">
                                                    <input class="input" style="box-sizing: border-box;">
                                                    <button class="button button_blue" style="margin: 0px;height: 2vw;padding: 0 !important;">添加</button>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </td>
                                <td class="fnTd">
                                    <div>内陆仓:10000</div>
                                    <div>海外仓:5806</div>
                                </td>
                                <td class="tableBtnBox">
                                    <!-- 计数器 -->
                                    <div class="computer" style="margin-left: auto;">
                                        <div class="iconfont icon-jianhao" onclick="compute(0,10,0)"></div>
                                        <div style="flex: 1;" class="count"> 1 </div>
                                        <div class="iconfont icon-tianjia1" onclick="compute(1,10,0)"></div>
                                    </div>
                                    <div class="gray" style="padding-top: 2px;">起订量:10 增量:10</div>
                                </td>
                            </tr>
                            <!-- -->
                            <tr>
                                <td>
                                    <div class="goodsInfo" style="place-items: center;">
                                        <div class="layui-form" style="margin-right: .5vw;">
                                            <input type="checkbox" name="BBB" checked>
                                        </div>
                                        <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                            <img src="../../images/icons/device3.png" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                        </div>
                                        <div class="goodsInformation"
                                            style="width: 60%;margin-left: 5%;margin-right: auto;">
                                            <div class="tdTitle flex"> 
                                                <div style="width: 60%;" class="textOver">220V转5V3.3V9V12V15V24V</div> 
                                                <div class="textSelect pointer" style="margin-left: auto;font-size: .7vw;" 
                                                onclick="toRouter(this)" data-link="../product/productDetails.html">商品详情></div>
                                            </div>
                                            <div>原厂编号<span class="name textOver">ACDC电源模块</span></div>
                                            <div>制造商: <span class="name textOver">Hi-Link</span> </div>
                                            <div class="textOver">制造商编号: <span
                                                    class="name textOver">727-S40FC008C3B1V000 </span>
                                            </div>
                                            <div class="textOver">
                                                型号:<span class="name textOver">HLK-PM01</span>
                                            </div>
                                            <div class="textOver flex">
                                                <div> 客户编号:</div>
                                                <div class="flex" style="margin: auto;width: 60%;">
                                                    <input class="input" style="box-sizing: border-box;">
                                                    <button class="button button_blue" style="margin: 0px;height: 2vw;padding: 0 !important;">添加</button>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </td>
                                <td class="fnTd">
                                    <div>内陆仓:10000</div>
                                    <div>海外仓:5806</div>
                                </td>
                                <td class="tableBtnBox">
                                    <!-- 计数器 -->
                                    <div class="computer" style="margin-left: auto;">
                                        <div class="iconfont icon-jianhao" onclick="compute(0,10,1)"></div>
                                        <div style="flex: 1;" class="count"> 1 </div>
                                        <div class="iconfont icon-tianjia1" onclick="compute(1,10,1)"></div>
                                    </div>
                                    <div class="gray" style="padding-top: 2px;">起订量:10 增量:10</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="funcBox flex">
                        <div class="layui-form flex" style="border: none;">
                            <input type="checkbox" name="qx" id="qx">
                            <label for="qx" class="pointer">全选</label>
                        </div>
                        <div style="margin-left: auto;margin-right: 1vw;">
                            <i class="iconfont icon-aixin2 textSelect iconSize"></i>
                            <span>加入心愿单</span>
                        </div>
                        <div>
                            <i class="iconfont icon-shanchu red1 iconSize"></i>
                            <span>删除订单</span>
                        </div>
                    </div>
                    <!-- 淘宝是不做分页的 滚动获取更多 -->
                    <!-- <div id="pagingBox" style="text-align: right;"></div>
                    <script>
                        function select(dom) {
                            const type = dom.getAttribute("data-type");
                            const parentDom = dom.parentNode;
                            $(parentDom).children().attr('class', '')
                            // console.log(parentDom,$(parentDom));
                            if (type == 0) {
                                dom.className = "bgSelect";
                            } else if (type == 1) {
                                dom.className = "bgSelect";
                            } else if (type == 2) {
                                dom.className = "bgSelect";
                            } else if (type == 3) {
                                dom.className = "bgSelect";
                            }
                        }
                        layui.use(function () {
                            var laypage = layui.laypage;
                            laypage.render({
                                elem: 'pagingBox',
                                count: 50, // 数据总数
                                theme: '#2C79E8'
                            });
                        });
                    </script> -->

                </div>
            </div>
            <div class="noData" data-show="false" style="display: none;">
                <img src="../../images/pics/konggouwuche.png" alt="" style="width: 30%;margin-top: 5%;">
                <div class="noDataText">您的购物车为空，去首页逛逛吧！</div>
                <button class="button button_blue noDataBtn"><i class="iconfont icon-home"
                        style="font-size: 1.2vw;"></i> 去首页 </button>
            </div>
        </div>
        <!-- 侧边栏 -->
        <div style="display: block;" class="aside">
            <div class="asideItem serverInfoIcon" style="color: black;"> 
                结算明细
            </div>
            <div class="asideItem" style="margin-top: 10px;">
                <div>商品总价</div>
                <div>¥ 5.66</div>
            </div>
            <div class="asideItem">
                <div>运费价格</div>
                <div>¥ 5.66</div>
            </div>
            <div class="asideItem" style="font-size: 16px;">
                <div style="color: black;">合计</div>
                <div class="red serverInfoIcon"> <b>¥ 5.66</b> </div>
            </div>
            <div class="asideItem borderB" style="margin:10px 0 15px 0">
               <button class="button button_blue" style="width: 95%;margin: 10px auto;border-radius: 45px;"
               onclick="toRouter(this)" data-link="../orders/checkOrder.html">去结算</button>
            </div>
            <!--  -->
            <div class="serverInfo">
                <div class="serverTitle" style="font-size: 15px;">服务申明</div>
                <div class="serverTitle flex"> 
                  <div class="iconfont icon-sign serverInfoIcon" style="margin-left: .2vw;margin-right: .2vw;"></div>
                  <div>快递</div>
                </div>
                <div class="serverItem flex">
                  <div class="iconfont icon-ai210"></div><div>支持七天无理由退货</div>
                </div>
                <div class="serverItem flex">
                  <div class="iconfont icon-ai210"></div><div>如果快递丢失，支持退货</div>
                </div>
                <div class="serverItem flex">
                  <div class="iconfont icon-ai210"></div><div>如果快递损坏，支持退货</div>
                </div>
                <div class="serverItem flex">
                  <div class="iconfont icon-ai210"></div><div>支持90天内免费换货</div>
                </div>
          
                <div class="serverTitle" > <i class="iconfont icon-secured serverInfoIcon"></i> 安全与隐私</div>
                <div class="serverItem" style="margin-top: 3px;">
                  <div>安全付款:未经您的同意，我们不会与任何第三方分享您的个人信息。</div>
                  <div style="margin-top: 3px;">安全的个人资料:我们保护您的隐私，确保您的个人资料安全可靠。</div>
                </div>
          
                <div class="serverTitle" >
                   <i class="iconfont icon-money-circle serverInfoIcon"></i> 
                   支付安全</div>
                <div class="serverItem" style="margin-top: 3px;">
                  <div class="paymentMethods flex" style="justify-content: left;margin-bottom: .5vw;">
                      <!-- <img src="../../images/icons/zhifubao.png" alt=""> -->
                      <img src="../../images/icons/paypal.png" alt="">
                      <img src="../../images/icons/weixin.png" alt="">
                  </div>
                  <div>与受欢迎的支付合作伙伴合作，您的个人信息是安全的。</div>
                </div>
            </div>
            <!-- 右侧边栏 -->
        <script>
            /** 节流函数 */
            function throttle(func, delay) {
                let timer = null;
                return function(...args) {
                    if (timer === null) {
                    timer = setTimeout(() => {
                        func.apply(this, args);
                        timer = null;
                    }, delay);
                    }
                };
            }
            function isAtFooter() {
                const footerRect = document.querySelector('.footer').getBoundingClientRect();
                const asideRect = document.querySelector('.aside').getBoundingClientRect();
                return asideRect.bottom > footerRect.top && asideRect.top < footerRect.bottom;
            }
            function handleScroll() {
                if (isAtFooter()) {
                    $('.aside').fadeOut(300);
                }else{
                    $('.aside').fadeIn();
                }
                // 处理滚动事件的逻辑
            }
        
            // 节流处理滚动事件
            const throttledScroll = throttle(handleScroll, 200);
            window.addEventListener('scroll', throttledScroll);
        </script>
        </div>
        <!-- 产品推荐 -国内才有 外国注重隐私 -->
        <div class="productComment" style="width: 90%;">
            <div class="title">
                相似推荐
            </div>
            <div class="mainBox2_container">
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star"
                           >4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star"
                           >4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star"
                           >4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star"
                           >4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star"
                           >4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
                <div class="mainBox2_content">
                    <div>
                        <img src="../../images/icons/device.png">
                    </div>
                    <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div class="gray">34人购买 <i class="iconfont icon-star"
                           >4.9</i>
                    </div>
                    <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    <!-- 加入购物车 -->
                    <div class="iconfont icon-icon1"></div>
                </div>
            </div>
        </div>

    </div>
    <!-- 底部开始 -->
    <div class="footer">
        <div class="footer2">
            <div class="footer1Content">
                <div>深圳市海凌科电子有限公司</div>
                <div>电话 : 0755-23152658</div>
                <div>邮箱 : <EMAIL></div>
                <div>地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>公司</div>
                <div>关于我们</div>
                <div>新闻中心</div>
                <div>品质保证</div>
                <div>提交工单</div>
                <div>企业社会责任</div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>资源</div>
                <div>新品速递</div>
                <div>代理招商</div>
                <div>应用场景</div>
                <div>服务和工具</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>支持</div>
                <div>联系我们</div>
                <div>帮助</div>
                <div>反馈</div>
                <div>Cookie政策</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <!--  -->
            <div class="footer1Content">
                <div class="connectOur">联系我们</div>
                <div class="footerLogo">
                    <img src="../../images/icons/weixin-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/youjian-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/wangzhi-landi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/skype-baidi.png" alt="" style="height: 100%;">
                </div>
                <div><img src="../../images/icons/gzh.png" alt="" style="width: 5vw;max-width: 75px;"></div>
                <div>关注微信订阅号</div>
                <div></div>
                <div></div>
            </div>
        </div>
        <div class="footer3">
            联系电话 : 0755-23152658 版权所有 : 深圳市海凌科电子有限公司 备案号 : 粤ICP备12055399号-1 技术支持 : 极思灵创
        </div>
    </div>
    <!-- 底部结束 -->
</body>

</html>
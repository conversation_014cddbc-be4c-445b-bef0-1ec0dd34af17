@charset "utf-8";
/* ================================ */
/* 商城前台购买\购物车等页面相关样式		*/
/* ================================ */

.wrapper { width: 1200px; margin: 0 auto;}
.ws0 { font-size: 0; *word-spacing: -1px/*IE6、7*/;}


/* 表单项属性
------------------------------------------- */
input[type="text"], input[type="password"], input.text, input.password { font: 12px/20px Tahoma; color: #777; background-color: #FFF; vertical-align: middle; display: inline-block; height: 20px; padding: 2px 4px; border: solid 1px #CCC; outline: 0 none;}
input[type="text"]:focus, input.text:focus, textarea:focus { color: #333; border-color: #75B9F0; box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15); outline: 0 none;}
input[type="text"].error, input[type="password"].error, textarea.error { border-color: #ED6C4F; box-shadow: 0 0 0 2px rgba(232, 71, 35, 0.15); outline: 0 none;}
textarea, .textarea { font: 12px/20px Tahoma; color: #777; background-color: #FFF; vertical-align: top; display: inline-block; height: 60px; padding: 2px 4px; border: solid 1px #CCC; outline: 0 none;}
select { color: #777; background-color: #FFF; vertical-align: top; display: inline-block; height: 26px; padding: 4px; border: solid 1px #CCC;}
select option { line-height: 20px; height: 20px;}
input[type="radio"], .radio { vertical-align: middle; display: inline-block; margin-right: 5px;}
/*表单验证错误提示文字*/
label.error { font-size: 12px; color: #E84723; margin-left: 8px;}
label.error i { margin-right: 4px;}
/* 按钮
-------------------------------------------*/
a.dsc-btn-mini { font: normal 12px/20px arial,"microsoft yahei"; color: #777; background-color: #F5F5F5; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 2px 10px; margin-right: 2px; border-style: solid; border-width: 1px; border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; cursor: pointer;}
a:hover.dsc-btn-mini { text-decoration: none; color: #333; background-color: #E6E6E6;border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF;}
a.dsc-btn { font: normal 14px/20px arial,"microsoft yahei"; color: #777; background-color: #F5F5F5; text-align: center; display: inline-block; height: 20px; padding: 8px 16px; border: solid 1px; border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; cursor: pointer;}
a:hover.dsc-btn { text-decoration: none; color: #333; background-color: #E6E6E6; border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF;}
a.dsc-btn-mini i, a.dsc-btn i { margin-right: 4px;}
a.dsc-btn-blue, a.dsc-btn-acidblue, a.dsc-btn-green, a.dsc-btn-orange, a.dsc-btn-red, a.dsc-btn-black,
a:hover.dsc-btn-blue, a:hover.dsc-btn-acidblue, a:hover.dsc-btn-green, a:hover.dsc-btn-orange, a:hover.dsc-btn-red, a:hover.dsc-btn-black { color: #FFF !important; text-shadow: 0 -1px 0 rgba(0,0,0,0.10);}
a.dsc-btn-blue { background-color: #006DCC; border-color: #0062B7 #0062B7 #005299 #0062B7;}
a.dsc-btn-acidblue { background-color: #49AFCD; border-color: #429DB8 #429DB8 #37839A #429DB8;}
a.dsc-btn-green { background-color: #5BB75B; border-color: #52A452 #52A452 #448944 #52A452;}
a.dsc-btn-orange { background-color: #FAA732; border-color: #E1962D #E1962D #BB7D25 #E1962D;}
a.dsc-btn-red { background-color: #DA4F49; border-color: #C44742 #C44742 #A33B37 #C44742;}
a.dsc-btn-black { background-color: #363636; border-color: #313131 #313131 #282828 #313131;}
a:hover.dsc-btn-blue { background-color: #0044CC; border-color: #003DB7 #003DB7 #003399 #003DB7;}
a:hover.dsc-btn-acidblue { background-color: #2F96B4; border-color: #2A87A2 #2A87A2 #237087 #2A87A2;}
a:hover.dsc-btn-green { background-color: #51A351; border-color: #499249 #499249 #3D7A3D #499249;}
a:hover.dsc-btn-orange { background-color: #F89406; border-color: #DF8505 #DF8505 #BA6F04 #DF8505;}
a:hover.dsc-btn-red { background-color: #BD362F; border-color: #AA312A #AA312A #8E2823 #AA312A;}
a:hover.dsc-btn-black { background-color: #222222; border-color: #1F1F1F #1F1F1F #191919 #1F1F1F;}

/*购物车样式  BEGIN*/
.dsc-header{display: block;width: 1200px;height: 100px;margin: 0 auto;overflow: hidden;}
.dsc-header .logo{float: left;width: 240px;height: 60px;margin: 20px 0;}
.dsc-header .logo img{max-width: 240px;max-height: 60px;}
.dsc-line{width: 100%;border-bottom:3px solid #ff4040;margin-bottom:20px;}
/* 步骤 */
.dsc-flow { font-size: 0; *word-spacing:-1px/*IE6、7*/; width: 640px; height: 70px; float: right; margin: 15px 0; }
.dsc-flow li { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; width: 25%; height: 70px; position: relative; z-index: 1;}
.dsc-flow li { *display: inline/*IE6,7*/;}
.dsc-flow li i { background: #FFF ; width: 30px; height: 30px; margin-left: -15px; position: absolute; z-index: 2; top: 0; left: 50%; font-size: 30px; color: #bfbfbf;}
.dsc-flow li p { font-size: 14px/24px arial,"microsoft yahei";  color: #CCC; text-align: center; width: 100%; height: 24px; position: absolute; z-index: 2; left: 0; bottom: 0; overflow: hidden;}
.dsc-flow li sub { background-color: #FFF; width: 5px; height: 5px; margin-left: -3px; border: solid 1px #CCC; border-radius: 4px; position: absolute; z-index: 2; top: 38px; left: 50%;}
.dsc-flow li .hr { *line-height: 1px; background-color: #DDD; width: 100%; height: 1px; border: none 0; position: absolute; z-index: 1; top: 29px;left: 0;}
.dsc-flow li.current i { color: #27a9e3;}

.dsc-flow li.current p { color: #27A9E3;}
.dsc-flow li.current sub { background-color: #27A9E3; border-color: #27A9E3; box-shadow: 0 0 0 2px rgba(0,153,204,0.15);}
.dsc-flow li.current .hr { background-color: #27A9E3;}

/* 购物车内容部分-*/
.dsc-main{width: 1200px;margin: 0 auto;}
.dsc-title { height: 50px; padding: 15px 0; border: solid #F7F7F7; border-width: 1px 0 0;}
.dsc-title h3 { font: lighter 24px/30px arial,"microsoft yahei"; color: #555;}
.dsc-title h5 { font: 12px/20px arial,"microsoft yahei"; color: #AAA;}
.dsc-title h5 a { color: #27A9E3;}
.dsc-table-style { width: 100%; border-collapse: collapse; border-style: solid; border-width: 2px 1px; border-color: #AAA #DDD;}
.dsc-table-style thead th { line-height: 20px; color: #666; background-color: #F9F9F9; text-align: center; height: 20px; padding: 8px 0; border-bottom: solid 1px #DDD;}
.dsc-table-style tbody th { font-size: 12px; line-height: 20px; color: #333; height: 20px; background-color: #DAF0FE; padding: 8px; border-bottom: solid 1px #DDD;}
.dsc-table-style tbody th .store-sale { font-size: 12px; display: inline-block; float: right;}
.dsc-table-style tbody th .store-sale em { font-size: 12px; line-height: 14px; color: #FFF; background-color: #090; vertical-align: middle; display: inline-block; height: 14px; padding: 2px 6px; border-radius: 10px; margin-right: 4px;}
.dsc-table-style .store-sale .dsc-store-gift { vertical-align: middle; display: inline-block; width: 24px; height: 24px; margin-left: 4px; position: relative; z-index: 1;}
.dsc-table-style .store-sale .dsc-store-gift img { width: 24px; height: 24px; position: absolute; z-index: 1; top: 0; left: 0; }
.dsc-table-style .store-sale .dsc-store-gift:hover { z-index: 2; }
.dsc-table-style .store-sale .dsc-store-gift:hover img { display: block; width: 40px; height: 40px; top: -10px; left: -10px; box-shadow: 4px 4px 0 rgba(153,153,153,0.25);transition:ease-in-out 0.25s;}

.dsc-table-style tbody tr td { background-color: #FFF; padding: 8px 0; text-align: center; border-bottom: solid 1px #DDD;}
.dsc-table-style tbody tr.shop-list td { background-color: #FFFDEE;}
.dsc-table-style tbody tr td.pd-account { background-color: #FFAA01; padding: 2px;}
.dsc-table-style tfoot td { background: #F9F9F9; text-align: right; height: 30px; padding: 10px 0;}

.dsc-table-style a { color: #333;}
.dsc-table-style a:hover { color: #C00;}
.dsc-table-style i { margin-right: 4px;}
.dsc-table-style .dsc-goods-thumb { line-height: 0; background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 40px; height: 40px; overflow: hidden;}
.dsc-table-style .dsc-goods-thumb img { max-width: 40px; max-height: 40px; margin-top:expression(40-this.height/2); *margin-top:expression(20-this.height/2)/*IE6,7*/;}
.dsc-table-style .dsc-goods-info { display: block; }
.dsc-table-style .dsc-goods-info dt { line-height: 20px;}
.dsc-table-style .dsc-goods-info dd { line-height: 16px; color: #999; margin-top: 4px;}
.dsc-table-style .dsc-goods-info dd span.xianshi,.dsc-table-style .dsc-goods-info dd span.mgdiscount,.dsc-table-style .dsc-goods-info span.zengpin { color: #FFF; background-color: #FD6760; padding: 1px 4px;}
.dsc-table-style .dsc-goods-info dd span.xianshi strong,.dsc-table-style .dsc-goods-info dd span.mgdiscount strong,.dsc-table-style .dsc-goods-info dd span.groupbuy strong { font-size: 14px; font-weight: 600; margin: 0 2px;}
.dsc-table-style .dsc-goods-info dd span.xianshi em,.dsc-table-style .dsc-goods-info dd span.mgdiscount em,.dsc-table-style .dsc-goods-info dd span.buldling em { color: #FF0; margin: 0 2px;}
.dsc-table-style .dsc-goods-info dd span.groupbuy,.dsc-table-style .dsc-goods-info span.buldling { color: #FFF; background-color: #DA542E; padding: 1px 4px;}
.dsc-null-shopping { width:700px; height:180px; margin: 0 auto; position: relative; z-index: 1; overflow: hidden;padding:160px 0;}
.dsc-null-shopping i.ico{width:235px;height:180px;text-align:center;line-height:100px;color:#ff4040;display:block;font-size:100px;float:left;background: url(../images/buy/cart_empty.png) no-repeat 0 0;}
.dsc-null-shopping .fl{margin-left:25px;margin-top:20px;}
.dsc-null-shopping .fl h4 {line-height:50px;font-size:20px;color: #8a8888;margin-bottom:10px;}
.dsc-null-shopping .fl a{padding:5px 50px;color: #fff;height:30px;line-height:30px;border-radius:1px;margin-right:15px;display:block;float:left;font-size:14px;}
.dsc-null-shopping .fl a i{font-size:16px;margin-right:3px;}

/*商品赠品*/
.dsc-goods-gift { line-height: 20px; color: #FFF; background-color: #F60; vertical-align: top; display: inline-block; *display: inline/*IE6、7*/; *zoom: 1/*IE6、7*/; padding: 2px 6px;}
.dsc-goods-gift-list { font-size: 0; *word-spacing:-1px/*IE6、7*/; vertical-align: top; display: inline-block; *display: inline/*IE6、7*/; *zoom: 1/*IE6、7*/; }
.dsc-goods-gift-list li { vertical-align: top; display: inline-block; *display: inline; margin-right: 5px; *zoom: 1;}
.dsc-goods-gift-list .thumb { display: block; width: 24px; height: 24px; position: relative; z-index: 1;}
.dsc-goods-gift-list .thumb img { width: 24px; height: 24px; position: absolute; z-index: 1; top: 0; left: 0; }
.dsc-goods-gift-list .thumb:hover { z-index: 2; }
.dsc-goods-gift-list .thumb:hover img { display: block; width: 40px; height: 40px; top: -10px; left: -10px; box-shadow: 4px 4px 0 rgba(153,153,153,0.25);transition:ease-in-out 0.25s;}

.dsc-table-style a.add-substract-key { font: 14px/14px Arial; color: #777; text-decoration: none; background-color: #F5F5F5; letter-spacing: normal; word-spacing: normal; text-align: center; vertical-align: middle; display: inline-block; width:24px; height:24px;line-height:24px;border: solid 1px; border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; cursor: pointer; *display: inline/*IE6,7*/; *zoom: 1;}
.dsc-table-style a:hover.add-substract-key { color: #FFF; background-color: #49AFCD; border-color: #429DB8 #429DB8 #37839A #429DB8; text-decoration:none;}
.dsc-bottom { width: 100%; padding: 20px 0; overflow: hidden;}
.dsc-msg-textarea { vertical-align: top; height: 20px; resize: none; width: 400px;}
.dsc-msg-textarea:focus { height: 40px;}

.dsc-receipt-info { color: #777; padding: 9px 19px; border: solid 1px #EEE;}
.dsc-receipt-info-title { font-size: 0; *word-spacing:-1px/*IE6、7*/; display: block; height: 22px; padding: 4px 0;}
.dsc-receipt-info-title h3,
.dsc-receipt-info-title a { font: 16px/20px arial,"microsoft yahei"; color: #333; vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block;}
.dsc-receipt-info-title a { font-size: 12px; font-family: Arial, Helvetica, sans-serif; color: #27A9E3; margin-left: 10px;}
.dsc-receipt-info-title h3, .dsc-receipt-info-title a { *display: inline/*IE6,7*/;}
.dsc-receipt-info-title h3 strong { font: bold 18px/20px Verdana; color: #F30; margin-left: 4px;}
.dsc-receipt-info.current_box { color: #534535; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#3FFFF5CC', endColorstr='#3FFFF5CC');background:rgba(255,245,204,0.25); padding: 7px 17px; border: solid 3px #ff4040;}

.dsc-receipt-info .fcode-form { width: 400px; margin: 20px auto;}
.dsc-receipt-info .fcode-form .text { font-size: 16px; vertical-align: middle; padding: 4px; width: 300px; height: 24px;}
.dsc-receipt-info .fcode-form .button { font-weight: 600; color: #555; background-color: #EEE; vertical-align: middle; width: 80px; height: 34px; border: solid 1px #AAA; cursor: pointer;}
.dsc-receipt-info .fcode-hint { font: 18px/34px arial,"microsoft yahei"; color: #27a9e3; text-align: center; width: 600px; height: 34px; margin: 20px auto;}
.dsc-receipt-info .fcode-hint i { font-size: 24px; margin-right: 10px;}
.dsc-candidate-items p { color: #888;}
.dsc-candidate-items p:hover { color: #27a9e3}
.dsc-candidate-items p i { font-size: 16px; color: #27a9e3; vertical-align: middle; margin-right: 4px;}

/*购物车样式  END*/



/* 表单提交样式 */
.dsc-form-default { }
.dsc-form-default dl { font-size: 0; *word-spacing:-1px/*IE6、7*/; line-height: 20px; clear: both; padding: 0; margin: 0; overflow: hidden;}
.dsc-form-default dl:hover { background-color: #FFF5CC;}
.dsc-form-default dl:hover .hint { color: #666;}
.dsc-form-default dl.bottom { border-bottom-width: 0px;}
.dsc-form-default dl dt { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; text-align: right; display: inline-block; width: 15%; padding: 10px 1% 10px 0; margin: 0;}
.dsc-form-default dl dt { *display: inline/*IE6,7*/;}
.dsc-form-default dl dt i.required { font: 12px/16px Tahoma; color: #F30; vertical-align: middle; margin-right: 4px;}
.dsc-form-default dl dd { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; width: 84%; padding: 10px 0 10px 0; }
.dsc-form-default dl dd { *display: inline/*IE6,7*/;}
.dsc-form-default dl dd p { clear: both;}
.dsc-form-default dl dd select { margin-right: 5px;}
/* 收货人信息 */
.dsc-candidate-items ul { overflow: hidden;}
.dsc-candidate-items li { line-height: 20px; display: block; min-height: 20px; padding: 5px 0 6px 0; margin-top: -1px; border-top: dotted 1px #E6E6E6;}
.dsc-candidate-items input[type="radio"], 
.dsc-candidate-items .radio,
.dsc-candidate-items label,
.dsc-candidate-items a { font-size: 12px;  vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block;}
.dsc-candidate-items .radio,
.dsc-candidate-items label,
.dsc-candidate-items a { *display: inline-block/*IE6,7*/; zoom: 1;}
.dsc-candidate-items input[type="radio"], 
.dsc-candidate-items .radio { margin: 0 5px;}
.dsc-candidate-items label { line-height: 20px; display: inline-block;}
.dsc-candidate-items span { line-height: 20px; vertical-align: middle; display: inline-block; height: 20px;}
.dsc-candidate-items .true-name { font-weight: 600; margin-right: 10px;}
.dsc-candidate-items .address { text-overflow: ellipsis; white-space: nowrap; max-width: 650px; margin-right: 20px; overflow: hidden;}
.dsc-candidate-items .phone { margin-right: 20px;}
.dsc-candidate-items label i { font-size: 14px; margin-right: 2px;}
.dsc-candidate-items .del { color: #ff4040;}
.dsc-selected-item { background-color: #FFF5CC; font-size: 12px;}


.dsc-table-style td h3 { float: left; width: 340px; font-size: 12px; font-weight: normal; padding-left: 20px; }
.dsc-table-style td h3 a { display: block; margin-bottom: 5px; color: #333; text-decoration: none; }
.dsc-table-style td h3 a:hover { color: #c00; text-decoration: underline; }
.dsc-table-style td h3 .attr { color: #b4b4b4; background-image: none;}


.dsc-store-account { margin: 0 20px 20px 0;}
.dsc-store-account dl { font-size: 0; *word-spacing:-1px/*IE6、7*/;}
.dsc-store-account dl dt, .dsc-store-account dl dd  { font: normal 12px/28px Verdana, Arial; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline; text-align: right; width: 100px; height: 28px; *zoom: 1;}
.dsc-store-account dl dt { color: #333; width: 300px;}
.dsc-store-account dl.freight dt, .dsc-store-account dl.freight dd { color: #999;}
.dsc-store-account dl.total dt, .dsc-store-account dl.total dd { color: #37839A; font-weight: 600;}
.dsc-store-account dl.mansong dt, .dsc-store-account dl.mansong dd, .dsc-store-account dl.voucher dd { color: #393;}
.dsc-store-account dl.mansong dt a { color: #393; text-decoration: underline;}
.dsc-store-account dl dd em { margin-right: 4px; vertical-align: middle;}
.dsc-store-account .chain dt{width: auto;}
.dsc-pd-account { color: #333; background-color: #FFF9ED; text-align: right; padding: 8px;}
.dsc-pd-account em { font-weight: 600; font-size: 12px; font-family: Verdana, Arial; color: #000; margin: 0 5px;}
.dsc-all-account { font: normal 16px/32px "microsoft yahei", Arial; margin-right: 20px; }
.dsc-all-account em { font-size: 22px; color: #ff4040; margin: 0 5px;}

/*支付方式*/
.dsc-payment-list { font-size: 0; *word-spacing:-1px/*IE6、7*/;}
.dsc-payment-list li { vertical-align: top; letter-spacing: normal; word-spacing: normal; text-align: center; display: inline-block; width: 120px; padding: 10px 30px; margin: 11px; border: solid 1px #E6E6E6; border-radius: 4px; position: relative; z-index: 1;}
.dsc-payment-list li i { display: none;}
.dsc-payment-list li label { display: block; cursor: pointer;}
.dsc-payment-list li:hover,
.dsc-payment-list li.using { border: solid 2px #52A452; margin: 10px;}
.dsc-payment-list li.using i { background: url(../images/buy/pay-use.png) no-repeat 0 0; display: block; width: 32px; height: 32px; position: absolute; z-index: 1; top: 2px; left: 0px;}
.dsc-payment-list li .logo { line-height: 0; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 120px; height: 50px; overflow: hidden;}
.dsc-payment-list li .logo img { max-width: 120px; max-height: 50px; margin-top:expression(50-this.height/2); *margin-top:expression(25-this.height/2)/*IE6,7*/;}
.dsc-payment-list li .predeposit { display: block; clear: both; margin-top: 10px;}
.dsc-payment-list li .predeposit p { font: 12px/18px "microsoft yahei"; color: #ff4040; text-align: left; margin-top: 5px;}

/*支付完成回执*/
.dsc-finish-a { font: 18px/32px "microsoft yahei"; height: 32px; margin-top: 20px;}
.dsc-finish-a i { background: url("../images/buy/pay-use.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0); display: inline-block; vertical-align: middle; margin-right: 8px; width: 32px; height: 32px; }
.dsc-finish-a span { font-weight: 600; color: #52A452; margin: 0 4px;}
.dsc-finish-a em { font: bold 18px/32px  Verdana, Geneva, sans-serif; color: #FF3300; margin: 0 4px;}
.dsc-finish-b, .dsc-finish-c { font: 14px/30px "microsoft yahei"; color: #999; margin: 5px 0 0 35px;}
.dsc-finish-b a { color: #27A9E3;}
.dsc-finish-c i { font-size: 14px; margin-right: 2px;}

.voucher_list { margin-bottom: 20px; }
table.voucher_list { margin: 0 auto 30px auto; border: 1px solid #C2C8CF;}
table.voucher_list { border-bottom-width: 0;}
table.voucher_list td { font-size: 12px; line-height: 20px; padding: 6px 10px; border-bottom: 1px solid #C2C8CF;}
table.voucher_list td.radio { color: #3E4646; text-align: right; width: 10%;}
table.voucher_list td.fashion { color: #3E4646; width: 15%;}
table.voucher_list td.pay { color: #3E4646; background: #F3F3F3; width: 25%;}
table.voucher_list td.explain { color: #9499A3; width: 50%; }


.nopay { line-height: 20px; background-color: rgb(254, 243, 224); float: left; color: #C30; margin-left: 60px; _margin-left: 30px; height: 20px; padding: 6px 12px; border: 1px solid #fbd0aa;}
.nopay a { font-weight: 600; color: #06C; }
/* 满即送活动 */
.ds-mansong { background-color:#F7F7F7; border: solid 1px #D8D8D8; padding: 4px;}
.ds-mansong-container { background-color:#FFF; padding: 10px 10px 10px 80px; margin: 0; position: relative; z-index: 1;}
.ds-mansong-container:hover { background-color:#F7F7F7;}
.ds-mansong-content {}
.ds-mansong-content dt {}
.ds-mansong-content dt h3 { color:#555; line-height: 24px; display:inline-block;}
.ds-mansong-content dt time { color:#999; line-height: 24px; font-size: 14px; font-weight:normal; display:inline-block; margin-left: 10px; margin-bottom: 5px;}
.ds-mansong-remark { color:#999; line-height: 24px;}



.tabs-hide { display: none !important;}

/* 积分兑换商品
------------------------------------------- */
.dsc-point-flow {}
.dsc-point-flow li {width: 33.3333% !important;}


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <link rel="stylesheet" href="../../modules/swiper/basic/css/idangerous.swiper.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/swiper/basic/js/idangerous.swiper.min.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
</head>
<style>
    .pageBox {
        width: 100%;
        height: 100vh !important;
        background-color: #F5F5F5;
    }

    .logo {
        width: 100%;
        padding: 1vw 0px;
        background-color: white;
    }

    .main {
        margin-left: 20%;
        padding: 1vw 0px 2vw 0px;
        width: 60%;
        /* height: 70vh; */
    }
</style>

<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="logo">
            <img src="../../images/icons/hlk.png" style="object-fit: contain;width: 137px;margin-left: 20vw;">
        </div>
        <!-- 头部结束 -->
        <div class="main">
            <!-- 面包屑 -->
            <div class="breadBox" style="border: none;">
                <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
                <div>></div>
                <div onclick="toRouter(this)" data-link="../userInfo/accountSettings.html">账户中心</div>
                <div>></div>
                <div class="textSelect" onclick="toRouter(this)" data-link="../userInfo/editMail.html">修改邮箱</div>
            </div>
            <div class="contaniner">
                <style>
                    .contaniner {
                        /* height: 60vh; */
                        min-height: 600px;
                        padding: 1vw;
                    }

                    .box {
                        width: 100%;
                        padding: 20px 10px;
                    }

                    .box:nth-child(n+3) {
                        background-color: white;
                        margin: 10px 0px;
                    }

                    .title {
                        font-size: 30px;
                        margin-bottom: 10px;
                        color: black;
                    }

                    .stepLine {
                        width: 30vw;
                        height: 3px;
                        border: 1px solid white;
                        border-radius: 5px;
                        transition: 1s;
                        position: relative;
                        background-color: var(--text-color4);
                    }

                    .box>.item {
                        /* border: 1px solid ; */
                        padding: 10px 0px;
                    }

                    .circle {
                        border-radius: 50%;
                        padding: 10px 15px;
                        margin-bottom: 5px;
                        background-color: var(--text-color4);
                        color: white;
                    }

                    .text {
                        font-size: 16px;
                    }

                    .tx {
                        width: 50px;
                        height: 50px;
                        border-radius: 50%;
                    }

                    .image {
                        margin-right: 5px;
                        border: 1px solid var(--line);
                    }

                    .contentBox>div {
                        margin-bottom: 10px;
                        /* border: 1px solid ; */
                        width: 50%;
                        min-width: 400px;
                    }

                    .input {
                        width: 15vw;
                        height: 35px;
                        max-width: 250px;
                    }

                    .button {
                        height: 40px;
                    }

                    .skyblueBtn {
                        border: 1px solid var(--blue-deep);
                        background: rgba(0, 148, 234, 0.15);
                        color: var(--blue-deep);
                    }

                    .skyblueBtn:hover {
                        color: rgb(49, 183, 236);
                        border: 1px solid rgb(49, 183, 236);
                    }

                    .icon-ai210 {
                        display: none;
                    }
                    .failText{
                        position: absolute;
                        top: 100%;
                        left: 10%;
                        color: var(--red);
                        font-size: 13px;
                        letter-spacing: 1px;
                        display: none;
                    }

                    /* 验证成功的css */
                    .circle[data-step="2"] {
                        padding: 10px;
                        background-color: var(--blue-deep);
                    }

                    .circle:first-child[data-step="2"] {
                        padding: 10px;
                    }

                    .one[data-step="2"] {
                        display: none;
                    }

                    .icon-ai210[data-step="2"] {
                        display: block;
                    }

                    .text[data-step="2"] {
                        color: var(--blue-deep);
                    }

                    .stepLine[data-step="2"] {
                        background-color: var(--blue-deep);
                    }
                    #contentBox1[data-step="2"] {
                        display: none;
                    }
                    #contentBox2[data-step="1"] {
                        display: none;
                    }
                </style>
                <div class="box" style="padding-top: 0px;">
                    <div class="title textC">修改邮箱</div>
                    <div class="title2 textC">修改邮箱后，原邮箱将无法登录，请知悉</div>
                </div>
                <div class="box flex" style="margin-bottom: 30px;">
                    <div class="item column">
                        <div class="circle bgSelect" data-step="2">
                            <span class="one" data-step="2">1</span>
                            <i class="iconfont icon-ai210" data-step="2" style="font-size: 20px;"></i>
                        </div>
                        <div class="iconfont icon-xuanzhong" data-step="2"></div>
                        <div class="text textSelect" data-step="2">验证身份</div>
                    </div>
                    <div class="item">
                        <div class="stepLine" data-step="2"></div>
                    </div>
                    <div class="item column">
                        <div class="circle" data-step="2" style="padding: 12px 16px;">
                            2
                        </div>
                        <div class="text" data-step="2">设置新邮箱</div>
                    </div>
                </div>


                <div class="box contentBox column" id="contentBox1" style="padding: 40px 0px;" data-step="2">
                    <div class="flex">
                        <div>
                            <img class="tx image" src="../../images/icons/morentouxiang.png">
                        </div>
                        <div style="margin: 0 auto 0 10px;" class="text">客户ID:1594586A</div>
                    </div>
                    <div>验证码发送至 <span class="textSelect" id="email">892****<EMAIL></span></div>
                    <div class="flex" style="margin: 20px 0px;">
                        <input type="text" class="input" placeholder="请输入验证码">
                        <button class="button button_blue skyblueBtn" id="getCodeBtn" onclick="getCode(this)"
                            data-codeStep="1" style="margin-right: auto;">获取验证码</button>
                    </div>
                    <div>
                        <button class="button button_blue" style="width: 55%;margin: 30px 30px 30px 30px;"
                            onclick="next(2)">下一步</button>
                    </div>
                </div>

                <!-- 设置邮箱 -->
                <div class="box contentBox column" id="contentBox2" style="padding: 40px 0px;" data-step="2">
                    <div class="flex">
                        <div>
                            <img class="tx image" src="../../images/icons/morentouxiang.png">
                        </div>
                        <div style="margin: 0 auto 0 10px;" class="text">客户ID:1594586A</div>
                    </div>
                    <div class="" style="margin: 20px 0px;">
                        <div class="flex" style="margin-bottom: 20px;position: relative;">
                            <div>邮箱：</div>
                            <div style="margin: 0 auto 0 10px;">
                                <input type="text" class="input" placeholder="请输入邮箱号" oninput="checkEmail(this)">
                            </div>
                            <div class="failText">您的邮箱格式错误</div>
                        </div>
                        <div class="flex">
                            <div style="opacity: 0;margin-right: 10px;">邮箱：</div>
                            <input type="text" class="input" placeholder="请输入验证码">
                            <button class="button button_blue skyblueBtn" id="getCodeBtn" onclick="getCode(this)"
                                data-codeStep="1" style="margin-right: auto;">获取验证码</button>
                        </div>
                    </div>
                    <div>
                        <button class="button button_blue" style="width: 60%;margin: 30px 30px 30px 35px;"
                            onclick="next(1)">完成</button>
                    </div>
                </div>
            </div>
        </div>
        <script>
            function next(step = 1) {
                const list = $('[data-step]');
                console.log(list);
                list.siblings().attr('data-step', step);
                $('.stepLine').attr('data-step', step); //奇怪
            }
            next()
            const email = document.getElementById('email').innerText
            document.getElementById('email').innerText = starNumber(email)
        </script>
    </div>


</body>

</html>
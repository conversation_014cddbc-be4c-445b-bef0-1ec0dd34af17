@charset "utf-8";
/* CSS Document */
/* ===============
 * 重新定义Html元素
 * =============== */
html, body, div, ul, ol, li, dl, dt, dd, h1, h2, h3, h4, h5, h6, pre, form, p, blockquote, fieldset, input { padding: 0; margin: 0;}
h1, h2, h3, h4, h5, h6, pre, code, address, caption, cite, code, em, strong, table, th, td { font-size: 1em; font-style: normal; font-weight: normal;}
strong { font-weight: bold;}
ul, ol { list-style: none outside none;}
fieldset, img { border: medium none; vertical-align: middle;}
caption, th {  text-align: left;}
table { border-collapse: collapse; border-spacing: 0;}
body { font: 12px/20px "microsoft yahei", Arial,Verdana,"宋体","Lucida Grande","Lucida Sans Unicode",Helvetica,sans-serif; color: #666; background: #FFF none repeat scroll 0 0; min-width: 1200px;}
input, select, textarea { font: 12px/20px Tahoma, Helvetica, Arial, "\5b8b\4f53", sans-serif;}
i, cite, em { font-style: normal;}
input,button,select,textarea{ outline:none}
checkbox { margin-right: 4px; vertical-align: middle;}
html {min-height:101%; }
a { text-decoration: none;}
a:hover { text-decoration: underline;}

/* ==========================
 * 为旧版本浏览器格式化Html5元素
 * ========================== */
article, aside, dialog, footer, header, section, footer, nav, figure, menu { display:block; }

/**/
.wrapper { width: 1000px; margin: 0 auto;}
.w100 { width: 100px !important;}
.w150 { width: 150px !important;}
.w200 { width: 200px !important;}
.w400 { width: 400px !important;}
.mt10 { margin-top: 10px;}
.mt15 { margin-top: 15px;}
.mt30 { margin-top: 30px;}
.tc { text-align: center;}


.dialog_wrapper { background-color: transparent !important; padding: 0 !important; box-shadow: none !important;}
.dialog_body { background-color: #f5f5f5 !important; border: none!important; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.25) !important;}
.dialog_head { border: none !important;}
.dialog_title { background-color: transparent !important;}
.dialog_content { padding: 0 10px 20px 10px !important;}
.dialog_content dl { font-size: 0; *word-spacing:-1px/*IE6、7*/; line-height: 20px; display: block; clear: both; overflow:hidden;}
.dialog_content dl dt {font: 14px/24px "microsoft yahei", Arial; color: #777;  vertical-align: top; letter-spacing: normal; word-spacing: normal; text-align: right; display: inline-block; *display: inline/*IE6,7*/; width: 19%; padding: 10px 1% 10px 0; margin: 0; zoom: 1;}
.dialog_content dl dd {font: 14px/24px "microsoft yahei", Arial;  vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE6,7*/; width: 79%; padding: 10px 0 10px 0; zoom:1;}

/* tip提示 */
.tip-yellowsimple { color:#000; background-color:#fff9c9; text-align:left; min-width:50px; max-width:300px; border:1px solid #c7bf93; border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px; z-index:1000; padding:6px 8px;}
.tip-yellowsimple .tip-inner { font:12px/16px arial,helvetica,sans-serif;}
.tip-yellowsimple .tip-arrow-top { background:url(../images/tip-yellowsimple_arrows.gif) no-repeat; width:9px; height:6px; margin-top:-6px; margin-left:-5px; top:0; left:50%;}
.tip-yellowsimple .tip-arrow-right { background:url(../images/tip-yellowsimple_arrows.gif) no-repeat -9px 0; width:6px; height:9px; margin-top:-4px; margin-left:0; top:50%; left:100%;}
.tip-yellowsimple .tip-arrow-bottom { background:url(../images/tip-yellowsimple_arrows.gif) no-repeat -18px 0; width:9px; height:6px; margin-top:0; margin-left:-5px; top:100%; left:50%;}
.tip-yellowsimple .tip-arrow-left { background:url(../images/tip-yellowsimple_arrows.gif) no-repeat -27px 0; width:6px; height:9px; margin-top:-4px; margin-left:-6px; top:50%; left:0;}



/* 调试模式 */
.trace { font-family:"Trebuchet MS", Arial, Helvetica, sans-serif; font-size:14px; color: #000; line-height: 18px; background:#FFF299; padding:8px; margin:8px; border:1px dashed silver;}
.trace fieldset { margin:8px;}
.trace fieldset legend { font-size: 16px; color: #000; font-weight:bold;}
.trace fieldset div { overflow:auto; max-height: 300px; _height: 300px; text-align:left;}

#footer { line-height: 20px; color: #b4baaa; text-align: center; display: block; width: 1000px; clear: both; margin: 10px auto 0 auto; padding-bottom: 10px; overflow: hidden;}
#footer p { color: #b4baaa; word-spacing: 5px; padding: 10px 0; }
#footer a { color: #b4baaa; text-decoration: none; }
#footer a:hover { text-decoration: underline; }
#footer .vol { font-family: Verdana, Geneva, sans-serif; font-weight: 600; font-style: oblique; font-size: 12px;}
#footer .vol .b { color: #00F;}
#footer .vol .o { color: #F60;}
#footer .vol em { font-family: Georgia, Arial;  font-weight: 600; font-style: italic; color: #000; margin-left: 2px;}

/* 翻页样式 */
.pagination { display: inline-block; margin: 0 auto;}
.pagination ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; }
.pagination ul li { vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; margin: 0 0 0 -1px;}
.pagination ul li { *display: inline/*IE6、7*/; *zoom:1;}
.pagination li span { font: normal 14px/20px "microsoft yahei"; color: #AAA; background-color: #FAFAFA; text-align: center; display: block; min-width: 20px; padding: 8px; border: 1px solid #E6E6E6; position: relative; z-index: 1;}
.pagination li a span , 
.pagination li a:visited span { color: #005AA0; text-decoration: none; background-color: #FFF; position: relative; z-index: 1;}
.pagination li a:hover span, .pagination li a:active span{ color: #FFF; text-decoration: none !important; background-color: #D93600; border-color: #CA3300; position: relative; z-index: 9; cursor:pointer;}
.pagination li a:hover { text-decoration: none;}
.pagination li span.currentpage { color: #AAA; font-weight: bold; background-color: #FAFAFA; border-color: #E6E6E6; position: relative; z-index: 2;}

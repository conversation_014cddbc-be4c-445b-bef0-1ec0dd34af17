<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>菜单按钮切换效果</title>
  <style>
    /* 隐藏默认的单选按钮外观 */
    input[type="radio"] {
      display: none;
    }

    /* 菜单按钮样式 */
    label {
      display: inline-block;
      padding: 10px 20px;
      background-color: #ccc;
      cursor: pointer;
      border-radius: 5px;
      margin-right: 10px;
    }

    /* 当对应的单选按钮被选中时，改变菜单按钮的背景颜色 */
    input[type="radio"]:checked + label {
      background-color: #007BFF;
      color: white;
    }
    .image-container {
  position: relative;
  width: 200px;
  height: 200px;
}

.image-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(var(--image-url));
  background-size: cover;
  background-position: center;
}
  </style>
</head>

<body>
  <input type="radio" id="option1" name="menuOptions" checked>
  <label for="option1">菜单1</label>

  <input type="radio" id="option2" name="menuOptions">
  <label for="option2">菜单2</label>

  <input type="radio" id="option3" name="menuOptions">
  <label for="option3">菜单3</label>
  <div class="image-container" data-image-url="image.jpg"></div>
</body>

</html>
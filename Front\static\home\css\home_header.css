/* 头部 BEGIN*/
.header{background-color: #FFF;width: 100%;}
.header .logo{float:left;margin:22px 100px auto 0;}
.header .logo img{ max-width: 240px;max-height: 46px}
/*头部搜索*/
.header .top_search{position:relative;z-index:100;float:left;margin:20px 0 0 20px;width:500px}
.header .top_search_box ul{position:absolute;margin:0 2px 0 0;width:60px;height:36px;border:2px solid #ff4040;border-right:none;background:#fff;text-indent:10px;font-size:1pc;font-family:Microsoft YaHei;line-height:36px;cursor:pointer}
.header .top_search_box li{padding:0 1px;height:36px;border-right:1px solid #ddd;background:#fff;color:#666;font-size:14px;line-height:36px}
.header .top_search_box li:nth-child(2){display:none;border-bottom:1px solid #ddd;border-left:1px solid #ddd}
.header .top_search_box li i{position:absolute;right:10px;width:0;height:0;border:3px dashed transparent;border-top:3px solid #999;font-size:0;line-height:0}
.header .top_search_box li i.arrow{top:17px;transition:all .2s ease-in 0s}
.header .top_search_box li i.over{top:17px;transform:rotate(180deg);transform-origin:50% 30% 0}
.header .top_search_box li:hover{background:#f3f3f3}
.header .form_fields{padding:2px;height:36px;background-color:#ff4040}
.header .form_fields .keyword{float:left;margin-left:60px;padding:6px 1%;width:69%;height:24px;border:0 none;color:#555;text-indent:5px;line-height:24px}
.header .form_fields .submit{float:left;width:79px;height:36px;border:none;background:#ff4040;color:#fff;font-size:1pc;font-family:Microsoft YaHei;line-height:1;cursor:pointer}
.header .top_search_keywords { float:left; width:100%;}
.header .top_search_keywords li {float:left; height:28px; line-height:28px; margin-right:15px;}
.header .top_search_keywords li.first{ border: none;}
.header .top_search_keywords li a {display:block; color:#999;}
.header .top_search_keywords li a:hover{ color:#e23435;}
/*头部右侧*/
.header .user_menu { font-size: 0; *word-spacing:-1px/*IE6、7*/; float: right; margin: 20px 0 0 0; }
.header .user_menu dl { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display:inline/*IE6、7*/; margin-left: 10px; position: relative; z-index: 3; *zoom:1/*IE6、7*/;}
.header .user_menu dl.my-mall { width: 108px;}
.header .user_menu dl.my-cart { width: 124px;}
.header .user_menu dl.hover { z-index: 101;}
.header .user_menu dl dt { line-height: 20px; color: #555; background-color: #FAFAFA; height: 20px; padding: 10px; border: solid 1px #F0F0F0; position: absolute; z-index: 2; top: 0; right: 0; cursor: pointer;}
.header .user_menu dl.my-mall dt { width: 90px;}
.header .user_menu dl.my-cart dt { width: 106px;}
.header .user_menu dl.hover dt { background-color: #FFF; border-width: 1px; border-color: #ff4040 #ff4040 #FFF #ff4040;}
.header .user_menu dl dt .ico {vertical-align: middle; display: inline-block; height:20px;line-height:20px;margin-right:4px;font-size:15px;float:left;}
.header .user_menu dl.my-mall .ico {}
.header .user_menu dl.my-cart .ico {font-size:16px;}
.header .user_menu dl dt i { font-size: 0px; line-height: 0; vertical-align: middle; display: inline-block; width: 0px; height: 0px; margin-left: 8px; border-width: 4px; border-color: #777 transparent transparent transparent; border-style:solid dashed dashed dashed; -webkit-transition: .2s ease-in; -moz-transition: -webkit-transform .2s ease-in; -o-transition: -webkit-transform .2s ease-in; transition: .2s ease-in;}
.header .user_menu dl.hover dt i { FILTER: progid:DXImageTransform.Microsoft.BasicImage(rotation=2); -moz-transform: rotate(180deg); -moz-transform-origin: 50% 30%; -webkit-transform: rotate(180deg); -webkit-transform-origin: 50% 30%; -o-transform: rotate(180deg); -o-transform-origin: 50% 30%; transform: rotate(180deg); transform-origin: 50% 30%;}
.header .user_menu dl dd { background-color: #FFF; display: none; border: solid 1px #ff4040; position: absolute; z-index: 1; top: 40px; right: 0; }
.header .user_menu dl.my-mall dd { width: 308px;}
.header .user_menu dl.my-cart dd { width: 348px;}
.header .user_menu dl.hover dd { display: block;}
.header .user_menu dl dd a { color: #005EA6;}
.header .user_menu a.arrow i {vertical-align: middle; display: inline-block; width: 4px; height: 7px; margin-left: 4px;}
.header .user_menu dl dd .goods-thumb { width: 50px; height: 50px;}
.header .user_menu dl dd .goods-thumb a { line-height: 0; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 48px; height: 48px; border: solid 1px #DDD; overflow: hidden; }
.header .user_menu dl dd .goods-thumb img { max-width: 48px; max-height: 48px; margin-top:expression(48-this.height/2); *margin-top:expression(24-this.height/2);}
.header .user_menu dl dd .sub-title { line-height: 28px; height: 28px; padding: 7px; border-bottom: solid 1px #DDD;}
.header .user_menu dl dd .sub-title h4 { font-weight: 600; float: left;}
.header .user_menu dl dd .sub-title a { float: right;}
.header .user_menu dl dd .user-centent-menu { width: 308px; padding: 8px 0; overflow: hidden;}
.header .user_menu dl dd .user-centent-menu ul { width: 310px; margin-left: -2px;}
.header .user_menu dl dd .user-centent-menu ul li { width: 138px; float: left; padding: 4px 0 4px 16px; border-left: solid 1px #EEE; }
.header .user_menu dl dd .browse-history { background-color: #FAFAFA; width: 308px; border-style: solid; border-width: 1px 0 0; border-color: #DDD transparent transparent;}
.header .user_menu dl dd .browse-history .part-title { height: 20px; padding: 6px 8px; }
.header .user_menu dl dd .browse-history .part-title h4 { float: left;}
.header .user_menu dl dd .browse-history .part-title a { float: right;}
.header .user_menu dl dd .browse-history ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; padding: 0 7px 7px 7px;}
.header .user_menu dl dd .browse-history ul li { vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display:inline/*IE6、7*/; margin: 0 4px; *zoom: 1;}
.header .user_menu dl dd .browse-history .no-goods { text-align: center; display: block; width: 100%; font-size: 12px;}
.header .user_menu dl dd .incart-goods-box { display: block; width: 348px; max-height: 267px; overflow: hidden; position: relative;}
.header .user_menu dl dd .incart-goods { text-align: center; width: 100%;}
.header .user_menu dl dd .incart-goods .no-order { margin: 20px 0;}
.header .user_menu dl dd .incart-goods .loading { width: 16px; height: 16px; margin: 20px auto;}
.header .user_menu dl dd .incart-goods dl { display: block; width: 98%; height: 66px; clear: both; margin: 0 auto 0 2%; border-bottom: dotted 1px #DDD; position: relative; z-index: 1; }
.header .user_menu dl dd .incart-goods dt, .header .user_menu dl dd .incart-goods dd { background-color: transparent; border: 0; position: absolute; z-index: 1; }
.header .user_menu dl dd .incart-goods dt.goods-name { line-height: 16px; background-color: transparent; text-align: left; width: 180px; height: 32px; padding: 0; top: 8px; left: 64px; }
.header .user_menu dl dd .incart-goods dt.goods-name a { color: #333;}
.header .user_menu dl dd .incart-goods dd.goods-thumb { top: 8px; left: 8px;}
.header .user_menu dl dd .incart-goods dd.goods-price { text-align: right; width: 80px; height: 16px; top: 6px; right: 12px;}
.header .user_menu dl dd .incart-goods dd.goods-price em { font-size: 11px !important; font-weight: normal!important; line-height: 16px; color: #777 !important; word-break: break-all; word-wrap: break-word; text-overflow: ellipsis; white-space: nowrap; text-align: right; width: 50px; margin-right: 4px; overflow: hidden; -webkit-text-size-adjust:none;}
.header .user_menu dl dd .incart-goods dd.goods-sales { line-height: 16px; color: #999; background-color: transparent; width: 270px; height: 16px; top: 42px; left: 64px; }
.header .user_menu dl dd .incart-goods dd.handle {  width: 32px; top: 22px; right: 10px;}
.header .user_menu dl dd .checkout { background-color: #F0F0F0; text-align: right; padding: 8px; overflow: hidden;}
.header .user_menu dl dd .checkout .total-price { line-height: 20px; display: block;}
.header .user_menu dl dd .checkout .total-price i { font-weight: 600; color: #C00; margin: 0 4px;}
.header .user_menu dl dd .checkout .total-price em { font-family: Verdana, Geneva, sans-serif; font-size: 18px; font-weight: 600; color: #C00;}
.header .user_menu dl dd .checkout .btn-cart { float: right; clear: both; line-height: 20px; color: #FFF; background-color: #ff4040; padding: 4px 8px; margin-top: 4px; border-radius: 4px;}
.header .user_menu dl div.addcart-goods-num { font: bold 11px/14px Verdana; color: #FFF; background: #F00; text-align: center; display: inline-block; height: 16px; min-width: 16px; border: none 0; border-radius: 8px; position: absolute; z-index: 3; top: -4px; left: 20px;}
/* 头部 END*/


/* 导航 BEGIN*/
.mall_nav{width:100%;height:40px;margin:0 auto;position:relative;z-index:99;zoom:1;border-bottom: 2px solid #ff4040;}
/*左侧分类相关*/
.mall_nav .all_categorys{background-color:#ff4040;display:block;width:212px;height:40px;float:left}
.mall_nav .all_categorys .mt{line-height:40px;height:40px;padding:0 10px}
.mall_nav .all_categorys .mt i{font-size:0;line-height:0;width:18px;height:14px;float:left;margin:4px 4px 0 0}
.mall_nav .all_categorys .mt h3 a{font-size:16px;font-weight:400;color:#FFF;float:left;margin-left:4px;}
.mall_nav .all_categorys .mc{width:212px;display:none;}
.mall_nav .all_categorys:hover .mc{display:block;}
.mall_nav .all_categorys .mc .menu{height:398px;background:#333;background:rgba(0,0,0,.8);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#99000000", endColorstr="#99000000")\9;padding-top:2px;}
.mall_nav .all_categorys .mc .menu li { width: 212px; height: 36px; *float: left; *clear: left; position: relative; z-index: 1; zoom:1;}
.mall_nav .all_categorys .mc .menu li.hover { z-index: 2;}
.mall_nav .all_categorys .mc .menu li:last-child { height: 37px;}
.mall_nav .all_categorys .mc .class { display: block; width: 183px; height: 16px; padding: 9px 15px; position: absolute; z-index: 1; top: 0; left: 0; -webkit-transition: all 0.3s; -moz-transition: all 0.3s; transition: all 0.3s;}
.mall_nav .all_categorys .mc .hover .class { background-color: #fff; width: 175px; padding: 9px 15px 9px 23px; z-index: 2;}
.mall_nav .all_categorys .mc .class .iconfont { vertical-align: top; display: inline-block; width: 16px; height: 16px; margin-right:6px;color:#ff4040;}
.mall_nav .all_categorys .mc .class .ico{width: 16px; height: 16px; margin-right:6px;}
.mall_nav .all_categorys .mc .class .ico img{width:16px;height:16px;float:left;}
.mall_nav .all_categorys .mc .class h4 { vertical-align: top; display: inline-block;*display: inline/*IE7*/; *zoom: 1/*IE7*/;}
.mall_nav .all_categorys .mc .class h4 a { color: #FFF; font-size: 13px; line-height: 16px; height: 16px;width:145px;overflow:hidden;display:block;}
.mall_nav .all_categorys .mc .menu li.hover .class h4 a { color: #333;}
.mall_nav .all_categorys .mc .class .arrow {width: 3px; height: 5px; float: right; margin: 5px 0 0;}
.mall_nav .all_categorys .mc .sub-class { background-color: #FAFAFA; display: none; width: 946px; min-height: 370px; padding: 10px 20px 19px 20px;border:1px solid #ff4040;position: absolute; z-index: 1;top:-1px; left: 212px; }
.mall_nav .all_categorys .mc li.hover .sub-class { display: block;}
.mall_nav .all_categorys .mc .sub-class-content { display: block; width: 730px; float: left;}
.mall_nav .all_categorys .mc .recommend-class { font-size: 0;  word-spacing:-1em; display: block; overflow: hidden;}
.mall_nav .all_categorys .mc .recommend-class span { font-size: 12px; background-color: #7C7171; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; margin: 0 10px 10px 0; *zoom: 1/*IE7*/;}
.mall_nav .all_categorys .mc .recommend-class span a { color: #FFF; line-height: 20px; display: block; padding: 2px 10px;}
.mall_nav .all_categorys .mc .sub-class dl {  font-size: 0;  word-spacing:-1em; padding-top: 10px;}
.mall_nav .all_categorys .mc .sub-class dl dt,
.mall_nav .all_categorys .mc .sub-class dl dd { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; *zoom: 1/*IE7*/;}
.mall_nav .all_categorys .mc .sub-class dl dt {width: 70px; height: 16px; padding-right: 15px; margin-right: 15px; }
.mall_nav .all_categorys .mc .sub-class dl dt h3 { line-height: 16px; font-weight: 600; color: #5C5251; text-align: right;}
.mall_nav .all_categorys .mc .sub-class dl dd { font-size: 0;  word-spacing:-1em; width: 630px; border-bottom: solid 1px #F0F0F0;}
.mall_nav .all_categorys .mc .sub-class dl:last-child dd { border-bottom: none 0;}
.mall_nav .all_categorys .mc .sub-class dl dd a { font-size: 12px; color: #7C7171; line-height: 14px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display:inline/*IE6、7*/; height: 14px; padding: 0 10px; margin-bottom: 10px; border-left: solid 1px #E7E7E7; *zoom:1/*IE6、7*/;}
.mall_nav .all_categorys .mc .sub-class dl dd a:hover { color: #C81623;}
.mall_nav .all_categorys .mc .sub-class dl dd h5 { font-size: 12px; line-height: 20px; color: #333; margin: 4px 0 2px 0;}

.mall_nav .all_categorys .mc .sub-class-right { display: block; width: 194px; height: 370px; float: right;}
.mall_nav .all_categorys .mc .brands-list {}
.mall_nav .all_categorys .mc .brands-list ul {font-size: 0;  word-spacing:-1em; height: 65px; overflow: hidden;}
.mall_nav .all_categorys .mc .brands-list li { font-size: 12px; background-color: transparent !important; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; width: 94px; height: 31px; margin: 0 1px 3px 2px; *zoom: 1/*IE7*/;}
.mall_nav .all_categorys .mc .brands-list li a { display: block; position: relative; z-index: 1;} 
.mall_nav .all_categorys .mc .brands-list li a img { width: 94px; height: 31px; position: absolute; z-index: 2; top: 0; right: 0; bottom: 0; left: 0;}
.mall_nav .all_categorys .mc .brands-list li a span { color: #333; line-height: 25px; background-color: #FFF; white-space: nowrap; text-align: center; width: 86px; height: 25px; padding: 3px 4px; position: absolute; z-index: 1; top: 0; right: 0; overflow: hidden;}
.mall_nav .all_categorys .mc .brands-list li a:hover span { color: #FFF; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#F27C7171', endColorstr='#F27C7171');background:rgba(124,113,113,0.95); z-index: 3;}
.mall_nav .all_categorys .mc .adv-promotions { display: block; width: 190px; margin: 0 2px;}
.mall_nav .all_categorys .mc .adv-promotions a { background: #FFF; display: block; width: 190px; height: 150px; margin-top: 10px; }
.mall_nav .all_categorys .mc .adv-promotions img { background: #D3D3D3; width: 190px; height: 150px;}


.category-ico-1:before{content: "\e601";}
.category-ico-2:before{content: "\e648";}
.category-ico-3:before{content: "\e670";}
.category-ico-4:before{content: "\e60c";}
.category-ico-5:before{content: "\e671";}
.category-ico-6:before{content: "\e63b";}
.category-ico-7:before{content: "\e6de";}
.category-ico-8:before{content: "\e645";}
.category-ico-9:before{content: "\e603";}
.category-ico-10:before{content: "\e655";}
.category-ico-11:before{content: "\e68a";}


/*右侧导航相关*/
.mall_nav .nav_list{max-width:988px;float:left;overflow:hidden;height: 40px;}
.mall_nav .nav_list .site_menu li { vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display:inline/*IE6、7*/; *zoom: 1;}
.mall_nav .nav_list .site_menu li a {  font-size: 16px; font-weight: normal; line-height:40px; color: #333; height: 40px; padding:0px 20px; display: inline-block; *display:inline/*IE6、7*/; zoom:1;}
.mall_nav .nav_list .site_menu li a:hover { text-decoration: none; color: #ff4040;}
.mall_nav .nav_list .site_menu li a.current { font-weight: 600; color: #ff4040; }
/* 导航 END*/

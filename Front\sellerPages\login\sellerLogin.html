<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家登录</title>
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../script/index.js"></script>
    <script src="../../modules/crypto.js"></script>
</head>
<style>
    .login_bg_box {
        margin: 10px 0px  1vw 0;
        min-height: 500px;
        background-color: #00397E;
        position: relative;
    }

    .loginImageBox {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 0;
    }

    .loginBox {
        margin-left: auto;
        margin-right: 20%;
        width: 400px;
        height: 420px;
        padding: 10px;
        background-color: white;
        position: relative;
        z-index: 10;
    }

    .loginBox>section {
        margin-left: 20px;
        margin-bottom: 10px;
    }

    .loginBox>.tips {
        color: var(--text-color2);
        margin-left: 20px;
        font-size: 13px;
    }

    .loginBox>.failText {
        display: none;
        margin-top: -4px;
        margin-bottom: 0;
        color: var(--danger);
        letter-spacing: 2px;
    }

    .loginBox>section>button {
        margin-top: 20px;
        margin-left: 0px;
        width: 90%;
    }

    .loginBox>.inputTile {
        margin-bottom: 2px;
        color: var(--text-color1);
    }
</style>

<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="header">
            <div style="margin-left: auto;"></div>
            <img src="../../images/icons/hlk.png" style="object-fit: contain;" class="headLogo">
            <div class="headerItem" style="width: 35%;min-width: 450px;">
                <div class="searchBox">
                    <span class="layui-form headerForm">
                        <select class="select" name="select" id="" style="font-size: 1em;" title="分类">
                            <option value="1">商家</option>
                            <option value="2">品牌</option>
                            <option value="3">型号</option>
                            <option value="4" selected>产品规格</option>
                        </select>
                    </span>

                    <span class="line"> </span>

                    <input class="searchInput" placeholder="请输入产品规格/型号/品牌/商家">
                    <button class="searchButton flex">
                        <div class="iconfont icon-search" style="margin-right: 5px;font-size: 20px;"></div>
                        搜索
                    </button>
                    <!-- @* 推荐关键字 *@ -->
                    <div class="header_keyBox flex">
                        <input class="header_keyCheck" type="radio" id="option1" name="menuOptions" checked>
                        <label class="header_key textOver" for="option1">PM01</label>

                        <input class="header_keyCheck" type="radio" id="option2" name="menuOptions">
                        <label class="header_key textOver" for="option2">LD2451</label>

                        <input class="header_keyCheck" type="radio" id="option3" name="menuOptions">
                        <label class="header_key textOver" for="option3">B0505S-1WR3</label>
                    </div>

                </div>
            </div>
            <div class="headerItem" style="width: 130px;">
                <div class="flex textOver">
                    <input type="checkbox" name="" id="goods1">
                    <label for="goods1" style="margin: -4px 5px;">库存量</label>
                </div>
                <div class="flex textOver" style="margin-left: auto;">
                    <input type="checkbox" name="" id="goods2">
                    <label for="goods2" style="margin: -4px 5px;">PHUJ</label>
                </div>
            </div>
            <div class="headerItem acountBox"
                style="color: var(--text-color);font-size: 1.1em;position: relative;margin-left: auto;">
                <!-- 登录后 -->
                <div class="isLogin" data-login="true">
                    <i class="iconfont icon-weidenglu icon" style="font-size: 1.4em !important;margin-right: 12px;"></i>
                    <span style="cursor: default;">账号信息</span>
                </div>
                <!-- 未登录 -->
                <div class="isLogin flex" data-login="false" style="font-size: 14px;">
                    <i class="iconfont icon-weidenglu" style="font-size: 1.8em !important;margin: -3px 3px 0 0;"></i>
                    <div class="hoverText textOver" onclick="toRouter(this)" data-link="../login/login.html">登录</div>/
                    <div class="hoverText textOver" onclick="toRouter(this)" data-link="../login/register.html">注册</div>
                </div>
                <style>
                    .acountBox:hover .acountInfo {
                        display: flex;
                    }

                    .acountInfo {
                        position: absolute;
                        top: 90%;
                        width: 150%;
                        padding: 1vw;
                        border: 1px solid var(--line);
                        box-shadow: 4px 2px 8px 0px rgba(0, 0, 0, 0.15);
                        border-radius: 24px 24px 24px 24px;
                        border: 1px solid #E2E3E9;
                        display: flex;
                        flex-direction: column;
                        background-color: white;
                        z-index: 99999999999999;
                        display: none;
                    }

                    .acountHead {
                        padding: .2vw 0px 1vw 0px;
                        height: 50px;
                        border-bottom: 1px solid var(--line);
                    }

                    .acountHead>div:nth-child(1) {
                        width: 30%;
                        margin-right: auto;
                    }

                    .acountHead>div:nth-child(2) {
                        width: 65%;
                        font-size: .6vw;
                    }

                    .acountHead>div>img {
                        width: 100%;
                    }

                    .acountArea>div>.icon {
                        margin-right: .7vw;
                    }

                    .acountArea {
                        padding-bottom: 1vw;
                        margin: 5px;
                        border-bottom: 1px solid var(--line);
                    }

                    .acountArea>div {
                        padding: .25vw 0px;
                        font-size: .75vw;
                        display: flex;
                        justify-content: left;
                        place-items: center;
                    }

                    .acountArea>div:hover {
                        color: var(--blue-deep);
                        cursor: pointer;
                    }

                    .loginOutBox>div {
                        padding: .5vw;
                        border-radius: 20px;
                        cursor: pointer;
                        text-align: center;
                        box-sizing: border-box;
                        border: 1px solid transparent;
                    }

                    /* .loginOutBox>div:hover{
                        background-color: var(--blue-deep);
                        border: 1px solid var(--line);
                        color: white;
                    } */
                    .loginOut {
                        margin-left: auto;
                        color: var(--red);
                        text-decoration: underline;
                        font-size: .5vw;
                        cursor: pointer;
                        display: none;
                    }

                    .acountHead[data-login="true"] {
                        display: flex;
                        /* display: none; */
                    }

                    .acountHead[data-login="false"] {
                        display: none;
                        /* display: flex; */
                    }
                </style>
                <div class="acountInfo">
                    <div class="loginOut isLogin" data-login="true">退出</div>
                    <div class="acountHead flex isLogin" data-login="true" data-login_flex="true">
                        <div class="">
                            <img src="../../images/icons/morentouxiang.png">
                        </div>
                        <div class="">
                            <div class="flex">
                                <div class="textSelect">138****5680</div>
                                <div style="margin-left: auto;">您好！</div>
                            </div>
                            <div style="margin-top: 0.2vw;white-space: nowrap;font-size: .52vw;">Hi~欢迎来到海凌科商城！</div>
                        </div>
                    </div>
                    <div class="loginOutBox isLogin" data-login="false" data-login_flex="true">
                        <div style=" background-color: var(--blue-deep);
                   border: 1px solid var(--line);
                   color: white;">登录</div>
                        <div>注册</div>
                    </div>
                    <div class="acountArea">
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-weidenglu icon"></div>
                            <div>账号信息</div>
                        </div>
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-a-description2x icon"></div>
                            <div>订单</div>
                        </div>
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-wuliu icon"></div>
                            <div>退货和退款</div>
                        </div>
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-a-cartline icon"></div>
                            <div>购物车</div>
                        </div>
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-heart icon"></div>
                            <div>心愿清单</div>
                        </div>
                    </div>
                    <div class="acountArea">
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-xiaoxitongzhi icon"></div>
                            <div>信息中心</div>
                        </div>
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-edit icon"></div>
                            <div>评价</div>
                        </div>
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-wuliuxinxi icon"></div>
                            <div>发票</div>
                        </div>
                    </div>
                    <div class="acountArea" style="border: none;padding-bottom: 0px;">
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-shezhi2 icon"></div>
                            <div>账户设置</div>
                        </div>
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-dianhua icon"></div>
                            <div>联系方式</div>
                        </div>
                        <div onclick="toRouter(this)">
                            <div class="iconfont icon-creditcard icon"></div>
                            <div>支付方式</div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="headerItem" style="color: var(--text-color);font-size: 1.1em;width: 80px;">
                <i class="iconfont icon-cart icon" style="font-size: 1.4em;margin-right: .2em;"></i>
                <span class="hoverText textOver">购物车</span>
            </div>
        </div>
        <!-- 头部分类 -->
        <div class="header2 textOver">
            <div class="flex productCategory" data-select="false" onclick="isSelect_productCategory(this)"
                style="margin-left: 10%;user-select: none;cursor: pointer;">
                <label for="select" class="iconfont icon-fenlei2" style="font-size: 26px;">
                </label> 产品分类 <i class="iconfont icon-xiangxia"></i>
                <style>

                </style>
                <div class="productCategoryBox">
                    <!-- 一级页面 -->
                    <div class="productCategory_onePage">
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> ACDC电源模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> DCDCD电源模组</span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 雷达模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> WIFI模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> BLE模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 人脸识别 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    </div>
                    <script>
                        // 监听鼠标hover事件
                        var productCategoryBox_dom = $(".productCategoryBox");
                        productCategoryBox_dom.mouseover(function (e) {
                            if (e.target.getAttribute("data-select")) {
                                $(e.target).siblings().attr("data-select", "false");
                                e.target.setAttribute("data-select", "true");
                            }
                        })
                    </script>

                    <div class="productCategory_hoverPages">

                        <div class="twoPage">
                            <div data-select="true" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> ACDC电源模组 </span>
                            </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> DCDCD电源模组</span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 雷达模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> WIFI模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> BLE模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 人脸识别 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 指纹模组 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 机壳电源 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模组 </span>
                            </div>
                        </div>

                        <div class="threePage">
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                        </div>
                        <div class="threePageMore">更多</div>
                    </div>
                    <!--  -->
                </div>
            </div>
            <span class="line2" style="height: 17px;margin-left: 10px;"></span>
            <div onclick="toRouter(this)" data-link="./index.html">
                <label class="header2_key">首页</label>
            </div>
            <!-- 第一版 -先干到海凌科品牌商去 -->
            <div onclick="toRouter(this)" data-link="../provider/providerDetail.html">
                <!-- <div onclick="toRouter(this)" data-link="../provider/provider.html"> -->
                <label class="header2_key">供应商</label>
            </div>
            <div>
                <label class="header2_key" onclick="toRouter(this)" data-link="../index/bomTool.html">Bom Tool</label>
            </div>
            <div>
                <label class="header2_key">RFQ</label>
            </div>
            <div>
                <label class="header2_key">技术资源</label>
            </div>
            <div>
                <label class="header2_key" onclick="toRouter(this)" data-link="../index/helper.html">帮助中心</label>
            </div>

            <div class="flex" style="margin-left: auto;margin-right: 10%;position: relative;">
                更改语言：<span class="lngText" onclick="showLngBoxFn()">中文</span>
                <div style="margin-left: 2vw;font-size: 13px;">
                    货币：
                    <img src="../../images/icons/zhongguo.png" style="width: 1.5vw;object-fit: contain;">
                    <span class="lngText" onclick="showLngBoxFn()"
                        style="color: dodgerblue;text-decoration: underline;">RMB￥</span>
                </div>
                <!-- 语言切换 & 货币切换盒子 -->
                <div class="showLngBox" id="showLngBox" data-show="false" style="display: none;">
                    <div>更改语言</div>
                    <div class="layui-form">
                        <select class="layui-select">
                            <option value="0">选择语言</option>
                        </select>
                    </div>
                    <div>货币</div>
                    <div style="position: relative;" class="layui-form">
                        <select class="layui-select">
                            <option class="option1">请选择</option>
                        </select>
                    </div>
                    <div style="margin-top: .5vw;">
                        <button class="layui-btn" onclick="confirmLngFn()"
                            style="background-color: var(--blue-deep);border-radius: 30px !important;">确定</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="login_bg_box flex">
            <div class="loginImageBox">
                <img src="../../images/pics/bg1.png" class="image">
            </div>
            <div class="loginBox">
                <section style="font-size: 25px;color: coral;">商家管理登录</section>
                <div class="tips" style="margin-top: 10px;">请输入您注册商铺时申请的商家名称</div>
                <div class="tips">登录密码为商城用户通用密码</div>
                <section class="inputTile" style="margin-top: 10px;">用户名</section>
                <section><input type="text" class="input" oninput="oninputFn(this,'usernameFail')"></section>
                <section class="failText" id="usernameFail">账号错误</section>
                <section class="inputTile">密码</section>
                <section><input type="text" class="input" oninput="oninputFn(this,'passwordFail')"></section>
                <section class="failText" id="passwordFail">密码错误</section>
                <section class="inputTile">验证码</section>
                <section class="flex">
                    <div style="margin-right: auto;"><input type="text" class="input"></div>
                    <div class="layui-col-xs5" style="padding: 5px 0;margin-right: auto;">
                        <div>
                            <img src="https://www.oschina.net/action/user/captcha"
                                onclick="this.src='https://www.oschina.net/action/user/captcha?t='+ new Date().getTime();">
                        </div>
                    </div>
                </section>
                <section>
                    <button class="button button_blue" onclick="login()">商家登录</button>
                </section>
                <div class="tips textR pointer hoverBlue" style="margin-right: 20px;">返回首页</div>
            </div>
            <script>
                function login() {
                    toRouter('../home/<USER>')
                }
                function oninputFn(dom, type) {
                    var val = dom.value;
                    if (val.length == 0) {
                        $('#' + type).css('display', 'none');
                        return
                    }
                    $('#' + type).css('display', 'block');
                }
            </script>
        </div>
    </div>
    <div class="bug"></div>
    <!-- 底部开始 -->
    <div class="footer">
        <div class="footer2">
            <div class="footer1Content">
                <div>深圳市海凌科电子有限公司</div>
                <div>电话 : 0755-23152658</div>
                <div>邮箱 : <EMAIL></div>
                <div>地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>公司</div>
                <div>关于我们</div>
                <div>新闻中心</div>
                <div>品质保证</div>
                <div>提交工单</div>
                <div>企业社会责任</div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>资源</div>
                <div>新品速递</div>
                <div>代理招商</div>
                <div>应用场景</div>
                <div>服务和工具</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>支持</div>
                <div>联系我们</div>
                <div>帮助</div>
                <div>反馈</div>
                <div>Cookie政策</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <!--  -->
            <div class="footer1Content">
                <div class="connectOur">联系我们</div>
                <div class="footerLogo">
                    <img src="../../images/icons/weixin-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/youjian-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/wangzhi-landi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/skype-baidi.png" alt="" style="height: 100%;">
                </div>
                <div><img src="../../images/icons/gzh.png" alt="" style="width: 5vw;max-width: 75px;"></div>
                <div>关注微信订阅号</div>
                <div></div>
                <div></div>
            </div>
        </div>
        <div class="footer3">
            联系电话 : 0755-23152658 版权所有 : 深圳市海凌科电子有限公司 备案号 : 粤ICP备12055399号-1 技术支持 : 极思灵创
        </div>
    </div>
    <!-- 底部结束 -->

</body>

</html>
﻿@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.LeftMenu = "Goods";
    ViewBag.LeftChileMenu = "GoodsAdd";
    long commonId = Model;
    var goodsCommon = GoodsCommon.FindById(commonId);
    var goods = Goods.FindById(commonId);
    var localizationSettings = LocalizationSettings.Current;
    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
    var goodsSkulist = GoodsSKUDetail.FindAllByGoodsId(goodsCommon?.Id ?? 0);

}
@await Html.PartialAsync("_Left")

<link rel="stylesheet" href="/static/home/<USER>/common.css">
<link rel="stylesheet" href="/static/home/<USER>/seller.css">
<link rel="stylesheet" href="/public/static/plugins/js/jquery-ui/jquery-ui.min.css">
<script>
    var BASESITEROOT = "";
    var HOMESITEROOT = "/static/home";
    var BASESITEURL = "http://b2b2c.h.com/index.php";
    var HOMESITEURL = "http://b2b2c.h.com/index.php/home";
</script>
<script src="/static/plugins/jquery-2.1.4.min.js"></script>
<script src="/public/static/plugins/js/jquery-ui/jquery-ui.min.js"></script>
<script src="/public/static/plugins/js/jquery-ui/jquery.ui.datepicker-zh-CN.js"></script>
<script src="/static/plugins/common.js"></script>
<script src="/static/plugins/jquery.validate.min.js"></script>
<script src="/static/plugins/additional-methods.min.js"></script>
<script src="/static/plugins/layer/layer.js"></script>
<script src="/static/home/<USER>/member.js"></script>
<script src="/static/plugins/js/dialog/dialog.js" id="dialog_js" charset="utf-8"></script>
<script>
    jQuery.browser = {};
    (function() {
    jQuery.browser.msie = false;
    jQuery.browser.version = 0;
    if (navigator.userAgent.match(/MSIE ([0-9]+)./)) {
    jQuery.browser.msie = true;
    jQuery.browser.version = RegExp.$1;
    }
    }
    )();
</script>
<div class="seller_right">
    <div class="seller_items">
    </div>
    <div class="p20">
        <ul class="add-goods-step">
            <li>
                <i class="icon iconfont">&#xe600;</i>
                <h6>STEP.1</h6>
                <h2>选择商品分类</h2>
                <i class="arrow iconfont">&#xe687;</i>
            </li>
            <li>
                <i class="icon iconfont">&#xe731;</i>
                <h6>STEP.2</h6>
                <h2>填写商品详情</h2>
                <i class="arrow iconfont">&#xe687;</i>
            </li>
            <li>
                <i class="icon iconfont">&#xe71d;</i>
                <h6>STEP.3</h6>
                <h2>添加商品SKU</h2>
                <i class="arrow iconfont">&#xe687;</i>
            </li>
            <li class="current">
                <i class="icon iconfont">&#xe6a2;</i>
                <h6>STEP.4</h6>
                <h2>上传商品图片</h2>
                <i class="arrow iconfont">&#xe687;</i>
            </li>
            <li>
                <i class="icon iconfont">&#xe64d;</i>
                <h6>STEP.5</h6>
                <h2>商品发布成功</h2>
            </li>
        </ul>
        <form method="post" id="goods_image" action="@Url.Action("CreateGoodsIamges")">
            <input type="hidden" name="commonid" value="@commonId">
            <div class="dssc-form-goods-pic">
                <div class="container" style="width:790px">
                    <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                        @if (localizationSettings.IsEnable)
                        {
                            <ul class="layui-tab-title">
                                <li data="" class="layui-this">标准 </li>
                                @foreach (var item in LanguageList)
                                {
                                    <li data="@item.Id" class="LId">@item.DisplayName</li>
                                }
                            </ul>
                        }
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                @foreach (var skuDetail in goodsSkulist)
                                {
                                    <div class="dssc-goodspic-list">
                                        <div class="title">
                                            <h3>@(skuDetail.SpecValueDetail(0))</h3>
                                        </div>
                                        <ul dstype="ul0">
                                            @{
                                                var bumPic = AlbumPic.FindByNameAndSId(goodsCommon?.GoodsImage ?? "", goodsCommon?.StoreId ?? 0);
                                                for (int i = 0; i < 5; i++)
                                                {
                                                    string dstypeStr = "file_0" + i;
                                                    if (bumPic != null && i == 0 && skuDetail.MaterialId == goods?.MerchantMaterial)//查找是否有商品图片，有则直接传入进去并且是属于该SKU
                                                    {
                                                        var gimage = GoodsImages.FindByUrlAndcId(bumPic.Name, commonId);
                                                        <li class="dssc-goodspic-upload">
                                                            <div class="upload-thumb">
                                                                <img src="@bumPic.Cover" dstype="@dstypeStr">
                                                                <input type="hidden" name="img[0][@i][name]" value="@bumPic.Name" dstype="@dstypeStr">
                                                                <input type="hidden" name="img[0][0][Id]" value="@gimage?.Id" dsId="@dstypeStr">
                                                            </div>
                                                            <div class="show-default selected" dstype="@dstypeStr">
                                                                <p>
                                                                    <i class="iconfont">&#xe64d;</i>
                                                                    默认主图                <input type="hidden" name="img[0][@i][default]" value="1">
                                                                </p>
                                                                <a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                            </div>
                                                            <div class="show-sort">
                                                                排序：
                                                                <input name="img[0][@i][sort]" type="text" class="text" value="0" size="1" maxlength="1">
                                                            </div>
                                                            <div class="dssc-upload-btn">
                                                                <a href="javascript:void(0);">
                                                                    <span>
                                                                        <input type="file" hidefocus="true" size="1" class="input-file" name="@dstypeStr" id="@dstypeStr">
                                                                    </span>
                                                                    <p>
                                                                        <i class="iconfont">&#xe733;</i>
                                                                        上传
                                                                    </p>
                                                                </a>
                                                            </div>
                                                        </li>
                                                    }
                                                    else
                                                    {
                                                        <li class="dssc-goodspic-upload">
                                                            <div class="upload-thumb">
                                                                <img src="/uploads/common/default_goods_image.jpg" dstype="@dstypeStr">
                                                                <input type="hidden" name="img[0][@i][name]" value="" dstype="@dstypeStr">
                                                            </div>
                                                            <div class="show-default" dstype="@dstypeStr">
                                                                <p>
                                                                    <i class="iconfont">&#xe64d;</i>
                                                                    默认主图                <input type="hidden" name="img[0][@i][default]" value="0">
                                                                </p>
                                                                <a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                            </div>
                                                            <div class="show-sort">
                                                                排序：
                                                                <input name="img[0][@i][sort]" type="text" class="text" value="@i" size="1" maxlength="1">
                                                            </div>
                                                            <div class="dssc-upload-btn">
                                                                <a href="javascript:void(0);">
                                                                    <span>
                                                                        <input type="file" hidefocus="true" size="1" class="input-file" name="@dstypeStr" id="@dstypeStr">
                                                                    </span>
                                                                    <p>
                                                                        <i class="iconfont">&#xe733;</i>
                                                                        上传
                                                                    </p>
                                                                </a>
                                                            </div>
                                                        </li>
                                                    }

                                                }
                                            }
                                        </ul>
                                        <div class="dssc-select-album">
                                            <a class="dssc-btn" href="/Sellers/SellerGoodsAdd/GoodsAlbumPic?demo=2" dstype="select-0">
                                                <i class="iconfont">&#xe72a;</i>
                                                从图片空间选择
                                            </a>
                                            <a href="javascript:void(0);" dstype="close_album" class="dssc-btn ml5" style="display: none;">
                                                <i class=" iconfont">&#xe67a;</i>
                                                关闭相册
                                            </a>
                                        </div>
                                        <div dstype="album-0"></div>
                                    </div>
                                }
                            </div>
                            @if (localizationSettings.IsEnable)
                            {
                                @foreach (var item in LanguageList)
                                {
                                    <div class="layui-tab-item">
                                        @foreach (var skuDetail in goodsSkulist)
                                        {
                                            <div class="dssc-goodspic-list">
                                                <div class="title">
                                                    <h3>@(skuDetail.SpecValueDetail(0))</h3>
                                                </div>
                                                <ul dstype="ul0@(item.Id)">
                                                    @{
                                                        var medellan = GoodsImagesLan.FindAllBySkuIdAndLId(skuDetail?.Id ?? 0, item.Id).FirstOrDefault();
                                                        for (int i = 0; i < 5; i++)
                                                        {
                                                            string dstypeStr = "@(item.Id)file_0" + i + "" + item.Id;
                                                            if (!@medellan.IsNull() && i == 0)//查找是否有商品图片，有则直接传入进去
                                                            {
                                                                var goodsImageslan = AlbumPic.FindByNameAndSId(medellan?.ImageUrl ?? "", goodsCommon?.StoreId ?? 0);
                                                                <li class="dssc-goodspic-upload">
                                                                    <div class="upload-thumb">
                                                                        <img src="@goodsImageslan?.Cover" dstype="@dstypeStr">
                                                                        <input type="hidden" name="[@item.Id].img[0][@i][name]" value="@goodsImageslan?.Name" dstype="@dstypeStr">
                                                                        <input type="hidden" name="[@item.Id].img[0][@i][Id]" value="@medellan?.Id" dstype="@dstypeStr">
                                                                    </div>
                                                                    <div class="show-default selected" dstype="@dstypeStr">
                                                                        <p>
                                                                            <i class="iconfont">&#xe64d;</i>
                                                                            默认主图                <input type="hidden" name="[@item.Id].img[0][@i][default]" value="1">
                                                                        </p>
                                                                        <a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                                    </div>
                                                                    <div class="show-sort">
                                                                        排序：
                                                                        <input name="[@item.Id].img[0][@i][sort]" type="text" class="text" value="0" size="1" maxlength="1">
                                                                    </div>
                                                                    <div class="dssc-upload-btn">
                                                                        <a href="javascript:void(0);">
                                                                            <span>
                                                                                <input type="file" hidefocus="true" size="1" class="input-file" name="@dstypeStr" id="@dstypeStr">
                                                                            </span>
                                                                            <p>
                                                                                <i class="iconfont">&#xe733;</i>
                                                                                上传
                                                                            </p>
                                                                        </a>
                                                                    </div>
                                                                </li>
                                                            }
                                                            else
                                                            {
                                                                <li class="dssc-goodspic-upload">
                                                                    <div class="upload-thumb">
                                                                        <img src="/uploads/common/default_goods_image.jpg" dstype="@dstypeStr">
                                                                        <input type="hidden" name="[@item.Id].img[0][@i][name]" value="" dstype="@dstypeStr">
                                                                    </div>
                                                                    <div class="show-default" dstype="@dstypeStr">
                                                                        <p>
                                                                            <i class="iconfont">&#xe64d;</i>
                                                                            默认主图 <input type="hidden" name="[@item.Id].img[0][@i][default]" value="0">
                                                                        </p>
                                                                        <a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                                    </div>
                                                                    <div class="show-sort">
                                                                        排序：
                                                                        <input name="[@item.Id].img[0][@i][sort]" type="text" class="text" value="@i" size="1" maxlength="1">
                                                                    </div>
                                                                    <div class="dssc-upload-btn">
                                                                        <a href="javascript:void(0);">
                                                                            <span>
                                                                                <input type="file" hidefocus="true" size="1" class="input-file" name="@dstypeStr" id="@dstypeStr">
                                                                            </span>
                                                                            <p>
                                                                                <i class="iconfont">&#xe733;</i>
                                                                                上传
                                                                            </p>
                                                                        </a>
                                                                    </div>
                                                                </li>
                                                            }

                                                        }

                                                    }
                                                </ul>
                                                <div class="dssc-select-album">
                                                    <a class="dssc-btn" href="/Sellers/SellerGoodsAdd/GoodsAlbumPic?demo=2" dstype="select-0@(item.Id)">
                                                        <i class="iconfont">&#xe72a;</i>
                                                        从图片空间选择
                                                    </a>
                                                    <a href="javascript:void(0);" dstype="close_album" class="dssc-btn ml5" style="display: none;">
                                                        <i class=" iconfont">&#xe67a;</i>
                                                        关闭相册
                                                    </a>
                                                </div>
                                                <div dstype="album-0@(item.Id)"></div>
                                            </div>
                                        }
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="sidebar">
                    <div class="alert alert-info alert-block" id="uploadHelp">
                        <div class="faq-img"></div>
                        <h4>上传要求：</h4>
                        <ul>
                            <li>1. 请使用jpg\jpeg\png等格式、单张大小不超过1M的正方形图片。</li>
                            <li>2. 上传图片最大尺寸将被保留为1280像素。</li>
                            <li>3. 每种颜色最多可上传5张图片或从图片空间中选择已有的图片，上传后的图片也将被保存在店铺图片空间中以便其它使用。</li>
                            <li>4. 通过更改排序数字修改商品图片的排列显示顺序。</li>
                            <li>5. 图片质量要清晰，不能虚化，要保证亮度充足。</li>
                            <li>6. 操作完成后请点下一步，否则无法在网站生效。</li>
                        </ul>
                        <h4>建议:</h4>
                        <ul>
                            <li>1. 主图为白色背景正面图。</li>
                            <li>2. 排序依次为正面图-&gt;背面图-&gt;侧面图-&gt;细节图。</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="bottom tc hr32">
                <input type="submit" class="submit" value="下一步，确认商品发布" />
            </div>
        </form>
    </div>
</div>
<script type="text/javascript" src="/static/plugins/ajaxfileupload.js" charset="utf-8"></script>
<script src="/static/plugins/jquery.ajaxContent.pack.js" type="text/javascript"></script>
<script src="/static/home/<USER>/sellergoods_add_step3.js"></script>
<script>
    var DEFAULT_GOODS_IMAGE = "/uploads/common/default_goods_image.jpg";

    $(function() {
        // 绑定所有"从图片空间选择"按钮
        $('a[dstype^="select-"]').off('click').on('click', function (e) {
            e.preventDefault();
            var $selectBtn = $(this);
            var $selectAlbum = $selectBtn.closest('.dssc-select-album');
            var $closeBtn = $selectAlbum.find('a[dstype="close_album"]');
            var $albumDiv = $selectAlbum.next('div[dstype^="album-"]');

            // 只关闭其他项
            $('a[dstype^="select-"]').not($selectBtn).show();
            $('a[dstype="close_album"]').not($closeBtn).hide();
            $('div[dstype^="album-"]').not($albumDiv).empty();

            // 只展开当前
            $selectBtn.hide();
            $closeBtn.show();

            // 加载相册内容
            $albumDiv.load($selectBtn.attr('href'));
        });

        // 绑定所有"关闭相册"按钮
        $('a[dstype="close_album"]').off('click').on('click', function () {
            var $closeBtn = $(this);
            var $selectAlbum = $closeBtn.closest('.dssc-select-album');
            var $selectBtn = $selectAlbum.find('a[dstype^="select-"]');
            var $albumDiv = $selectAlbum.next('div[dstype^="album-"]');
            $closeBtn.hide();
            $selectBtn.show();
            $albumDiv.empty();
        });
    });
</script>
<script src="/static/plugins/jquery.cookie.js"></script>
<script src="/static/home/<USER>/compare.js"></script>
<link rel="stylesheet" href="/static/plugins/perfect-scrollbar.min.css">
<script src="/static/plugins/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/plugins/js/qtip/jquery.qtip.min.js"></script>
<link href="/static/plugins/js/qtip/jquery.qtip.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/plugins/jquery.lazyload.min.js"></script>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script>
    var CreateImgs = "/Sellers/SellerGoodsAdd/UploadPicture";
</script>
<script asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            layer = layui.layer,
            element = layui.element;
    })

    var DEFAULT_GOODS_IMAGE = "/uploads/common/default_goods_image.jpg";
    var Querytheson = "@Url.Action("Querytheson", "ProductCategory")";
    var CreateImg = "@Url.Action("UploadImg")";
    var CreateImgs = "@Url.Action("UploadImg", "SpaceCategory", new { type = 1 })";

    var lid = "";
    var select = "";
    var del = "";
    var album = "";
    $(function () {
        $(".layui-tab-title").on("click", "li", function () {
            // 关闭所有打开的相册
            $('a[dstype^="select-"]').show();
            $('a[dstype="close_album"]').hide();
            $('div[dstype^="album-"]').empty();
            console.log($(this).attr("data"));
            lid = $(this).attr("data");
        })
        $(".layui-tab-title li.layui-this").trigger("click");
    });
    // 选择默认主图&&删除
    function selectDefaultImage($this) {
        // 默认主题
        $this.click(function () {
            $(this).parents('ul:first').find('.show-default').removeClass('selected').find('input').val('0');
            $(this).addClass('selected').find('input').val('1');
        });
        // 删除
        $this.parents('li:first').find('a[dstype="' + del + '"]').click(function () {
            $this.unbind('click').removeClass('selected').find('input').val('0');
            $this.prev().find('input').val('').end().find('img').attr('src', DEFAULT_GOODS_IMAGE);
        });
    }

    // 从图片空间插入主图
    function insert_img(name, src, color_id) {
        color_id = color_id + lid;
        var $_thumb = $('ul[dstype="ul' + color_id + '"]').find('.upload-thumb');
        $_thumb.each(function () {
            if ($(this).find('input').val() == '') {
                $(this).find('img').attr('src', src);
                $(this).find('input').val(name);
                selectDefaultImage($(this).next());      // 选择默认主图
                return false;
            }
        });
    }

    function insert_imgs(id, name, src, color_id) {
        color_id = color_id + lid;
        var $_thumb = $('ul[dstype="ul' + color_id + '"]').find('.upload-thumb');
        $_thumb.each(function () {
            if ($(this).find('input:eq(0)').val() == '') {
                $(this).find('img').attr('src', src);
                $(this).find('input:eq(0)').val(name);
                $(this).find('input:eq(1)').val(id);
                $(this).find('input:eq(2)').val(name);

                selectDefaultImage($(this).next());      // 选择默认主图
                return false;
            }
        });
    }
</script>
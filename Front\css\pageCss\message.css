
.breadBox {
    border-bottom: none;
}

.main {
    margin-left: 15%;
    width: 70%;
}

.userInfoBox {
    margin-bottom: 1vw;
    height: 70vh;
    display: flex;
    /* border: 1px solid ; */
}

aside {
    padding: 10px 0px;
    width: 15%;
    height: fit-content;
    border: 1px solid #E2E3E9;
}

aside>div:nth-child(1) {
    font-size: 20px;
    padding-top: 10px;
    text-indent: 15%;
    font-weight: 550;
}

aside>div:not(:first-child) {
    margin-bottom: .5vw;
    padding: 7px 0vw;
    font-size: 15px;
    display: flex;
    justify-content: left;
    place-items: center;
    width: 100%;
    cursor: pointer;
}

aside>div>div:nth-child(1) {
    margin-right: .5vw;
    margin-left: 15%;
    font-size: 20px;
}

._line {
    width: 80% !important;
    height: 0px !important;
    border: none;
    border-bottom: 1px solid var(--line);
    margin: 0px 0px .7vw 10% !important;
}

aside>div:not(.bgSelect):hover {
    color: var(--blue-deep);
}

.content {
    width: 80%;
    display: block;
    padding: 0vw 1vw 1vw 1vw;
}
/* <!-- 聊天框 --> */
.chatBox {
    position: relative;
    width: 90%;
    height: 85%;
    margin-left: 2.5%;
    /* padding: .5vw .5vw; */
    background: #FFFFFF;
    box-shadow: 2px 2px 3px 0px rgba(0, 0, 0, 0.1), -2px -2px 3px 0px rgba(0, 0, 0, 0.1);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #D9D9D9;
    box-sizing: border-box;
}

.chatAside {
    width: 30%;
    height: 100%;
    border-right: 1px solid #ececec;
    /* background-color: red; */
}

.chatContentBox {
    width: 75%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.searchInputBox {
    width: 90%;
    margin: 0vw auto .5vw auto;
    position: relative;
    background-color: white;
    font-weight: 500;

}

.search_input {
    width: 70% !important;
    padding: 0px .5vw 0px 2vw;
    height: 1.7vw;
    font-size: .8vw;
    background-color: white;
}

.search_input::placeholder {
    font-size: .65vw;
}

.inputIcon {
    position: absolute;
    top: .5vw;
    padding-left: .5vw;
    color: var(--text-color2);
    margin-right: 5px;
    font-size: .9vw;
}

.userList {
    max-height: 80%;
    height: 80%;
    overflow: auto;
}

.chatAside_top {
    width: 90%;
    margin: .7vw auto .5vw auto;
}

.userContent {
    padding: .5vw 0vw;
    width: 100%;
    border: 1px solid #f5f5f5;
    display: flex;
    box-sizing: border-box;
    cursor: default;
}

.avatar {
    margin-left: 5%;
    width: 2.5vw;
    height: 2.5vw;
    box-shadow: 0 0 4px darkblue;
    border-radius: 5px;
    border: 1px solid var(--blue-deep);
}

.avatar>img {
    margin-left: 5%;
    width: 90%;
    height: 100%;
    border-radius: 5px;
    object-fit: contain;
}

.userContent_right {
    margin-left: 4%;
    width: calc(100% - 4.5vw);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.userText {
    margin-top: .4vw;
    width: 100%;
    height: 40%;
    font-size: .65vw;
    color: var(--text-color2);
    /* display: flex;
place-items: center; */
}

/* // 美化滚动条 */
div::-webkit-scrollbar {
    width: .3vw;
}

div::-webkit-scrollbar-track {
    width: 6px;
    background: rgba(#101F1C, 0.1);
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
}

div::-webkit-scrollbar-thumb {
    background-color: #f2f2f2;
    background-clip: padding-box;
    min-height: 28px;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
    transition: background-color .3s;
    cursor: pointer;
}

div::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, .3);
}

.currentUser {
    background-color: #f2f4f5cc;
}

.chatContent_title {
    padding: .5vw 1vw;
    font-size: 1vw;
    border-bottom: 1px solid #f2f2f2;
}
/* <!-- 聊天内容 --> */
.chatContent {
    flex: 8;
}

.chatInputBox {
    /* overflow-x: hidden;
    overflow-y: auto; */
    background-color: #F5F5F5;
    position: relative;
    border: 1px solid var(--line);
    display: flex;
    max-height: 15vh;
    min-height: 30px;
}

.chatInputBoxIcon {
    font-size: 1.4vw;
    color: #919090;
    padding: .7vw .5vw;
}

.inputBox {
    width: 100%;
    background-color: white;
    /* border-radius: 22px; */
    display: flex;
    /* place-items: center; */
    overflow-y: auto;
    position: relative;
}

.inputBox_textarea {
    padding-top: .5vw;
    flex: 1;
    height: 30px !important;
    min-height: 30px;
    outline: none;
    border: none;
    padding-left: .5vw;
    color: var(--text-color1);
    letter-spacing: 1px;
    line-height: 20px;
    word-break: break-all;
    resize: none;
    font-size: .85vw;
}

.icon-biaoqingemoji {
    /* position: fixed; */
    /* right: 0; */
    font-size: 1.3vw;
    color: #919090;
    margin-right: .5vw;
    background-color: white;
}

.iconfont {
    cursor: pointer;
}

.emojiBox {
    position: absolute;
    bottom: 110%;
    right: 10%;
    padding: .5vw;
    width: 60%;
    height: 25vh;
    border-radius: 5px;
    box-shadow: 0 0 4px #ccc;
    z-index: 10;
    background-color: #f7f7f7;
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
}

.emojiBox>div {
    flex: 1;
    padding: .3vw;
    border: 1px solid transparent;
    cursor: pointer;
    border-radius: 2px;
    box-sizing: border-box;
    font-size: 1vw;
}

.emojiBox>div:hover {
    border: 1px solid red;
}
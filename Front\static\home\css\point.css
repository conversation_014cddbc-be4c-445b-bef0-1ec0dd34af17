@charset "utf-8";

/* 按钮
-------------------------------------------*/
a.dsp-btn-mini { font: normal 12px/20px "Microsoft Yahei"; color: #777; background-color: #F5F5F5; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 0 10px; margin-right: 2px; border-style: solid; border-width: 1px; border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; cursor: pointer;}
a:hover.dsp-btn-mini { text-decoration: none; color: #333; background-color: #E6E6E6;border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF;}
a.dsp-btn { font: normal 14px/20px "Microsoft Yahei"; color: #777; background-color: #F5F5F5; text-align: center; display: inline-block; height: 20px; padding: 4px 16px; border: solid 1px; border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; cursor: pointer;}
a:hover.dsp-btn { text-decoration: none; color: #333; background-color: #E6E6E6; border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF;}
a.dsp-btn-mini i, a.dsp-btn i { margin-right: 4px;}
a.dsp-btn-blue, a.dsp-btn-acidblue, a.dsp-btn-green, a.dsp-btn-orange, a.dsp-btn-red, a.dsp-btn-black,
a:hover.dsp-btn-blue, a:hover.dsp-btn-acidblue, a:hover.dsp-btn-green, a:hover.dsp-btn-orange, a:hover.dsp-btn-red, a:hover.dsp-btn-black { color: #FFF ; text-shadow: 0 -1px 0 rgba(0,0,0,0.10);}
a.dsp-btn-blue { background-color: #006DCC; border-color: #0062B7 #0062B7 #005299 #0062B7;}
a.dsp-btn-acidblue { background-color: #49AFCD; border-color: #429DB8 #429DB8 #37839A #429DB8;}
a.dsp-btn-green { background-color: #5BB75B; border-color: #52A452 #52A452 #448944 #52A452;}
a.dsp-btn-orange { background-color: #FAA732; border-color: #E1962D #E1962D #BB7D25 #E1962D;}
a.dsp-btn-red { background-color: #DA4F49; border-color: #C44742 #C44742 #A33B37 #C44742;}
a.dsp-btn-black { background-color: #363636; border-color: #313131 #313131 #282828 #313131;}
a:hover.dsp-btn-blue { background-color: #0044CC; border-color: #003DB7 #003DB7 #003399 #003DB7;}
a:hover.dsp-btn-acidblue { background-color: #2F96B4; border-color: #2A87A2 #2A87A2 #237087 #2A87A2;}
a:hover.dsp-btn-green { background-color: #51A351; border-color: #499249 #499249 #3D7A3D #499249;}
a:hover.dsp-btn-orange { background-color: #F89406; border-color: #DF8505 #DF8505 #BA6F04 #DF8505;}
a:hover.dsp-btn-red { background-color: #BD362F; border-color: #AA312A #AA312A #8E2823 #AA312A;}
a:hover.dsp-btn-black { background-color: #222222; border-color: #1F1F1F #1F1F1F #191919 #1F1F1F;}
/* =====================
 * 表单元素格式化及伪类效果
 * ===================== */
input[type="text"], input[type="password"], input.text, input.password { font: 12px/20px Tahoma; color: #777; background-color: #FFF; vertical-align: top; display: inline-block; height: 20px; padding: 4px; border: solid 1px #CCC; outline: 0 none;}
input[type="text"]:focus, input[type="password"]:focus, input.text:focus, input.password:focus, textarea:focus { color: #333; border-color: #75B9F0; box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15); outline: 0 none;}
textarea, .textarea { font: 12px/20px Tahoma; color: #777; background-color: #FFF; vertical-align: top; display: inline-block; height: 60px; padding: 4px; border: solid 1px #CCC; outline: 0 none; resize:none;}
select { color: #777; background-color: #FFF; height: 30px; padding: 4px; border: solid 1px #CCC;}
select option { line-height: 20px; height: 20px; padding: 4px;}
/* 翻页样式 */
.pagination { display: inline-block; margin: 0 auto;}
.pagination ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; }
.pagination ul li { vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; margin: 0 0 0 -1px;}
.pagination ul li { *display: inline/*IE6、7*/; *zoom:1;}
.pagination li span { font: normal 14px/20px "microsoft yahei"; color: #AAA; background-color: #FAFAFA; text-align: center; display: block; min-width: 20px; padding: 8px; border: 1px solid #E6E6E6; position: relative; z-index: 1;}
.pagination li a span , 
.pagination li a:visited span { color: #005AA0; text-decoration: none; background-color: #FFF; position: relative; z-index: 1;}
.pagination li a:hover span, .pagination li a:active span{ color: #FFF; text-decoration: none !important; background-color: #ff4040; border-color: #CA3300; position: relative; z-index: 9; cursor:pointer;}
.pagination li a:hover { text-decoration: none;}
.pagination li span.currentpage { color: #AAA; font-weight: bold; background-color: #FAFAFA; border-color: #E6E6E6; position: relative; z-index: 2;}

.dsp-container { width: 1200px; margin: 0 auto 10px auto;}




/*会员等级*/
.dsp-member-grade .progress-bar { font-size: 0; *word-spacing:-1px/*IE6、7*/; display: block;}
.dsp-member-grade .progress-bar em,
.dsp-member-grade .progress-bar span { vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; *zoom: 1;}
.dsp-member-grade .progress-bar em { font: 12px/20px "microsoft yahei"; text-align: center; width: 30px;}
.dsp-member-grade .progress-bar span { background-color: #CCC; width: 200px; height: 10px; border-radius: 5px;}
.dsp-member-grade .progress-bar span i { background-color: #ff4040; display: block; height: 10px; border-radius: 5px;}

/* 会员积分 */


/* 大图 */
.exchange-banner{ overflow: hidden; min-height:300px;}
.exchange-banner .exchange-banner-wp{ width: 1920px; text-align: center; margin-left: -360px; }
.act-banner{ overflow:hidden;}
.banner{position: relative}
.act-banner-wp{position: absolute; width: 100%; height:300px;text-align: center;background:url(../images/point_bg.jpg) center ;background-size:1920px 300px }
.w1200{width:1200px;margin: 0 auto;}
.relative{position: relative}
/* 会员未登录 */
.exchange-score{ position: absolute; z-index: 2; left: 0; top: 20px; padding-bottom:30px;background: #fff; width: 232px; height: auto;}
.exchange-score .u-info{ text-align: center; padding: 30px 40px 10px 40px;}
.exchange-score .u-info .u-avatar{ width: 80px; height: 80px; display: block; margin: 0 auto 5px; padding: 5px; border: 5px solid #CD1F0A; border-radius: 50%; }
.exchange-score .u-info .u-avatar img{ width: 80px; height: 80px; border-radius: 50%; }
.exchange-score .u-info .u-name{ font-size: 14px; overflow:hidden; display:block; text-overflow:ellipsis; white-space:nowrap;}
.exchange-score .score-info{ padding: 10px 0; overflow:hidden;}
.exchange-score .score-info .item{ float: left; white-space: nowrap; text-align: center; width: 103px;}
.exchange-score .score-info .item:first-child{ padding-right: 10px; border-right: 1px solid #ddd; margin-right: 10px;}
.exchange-score .score-info .item .num{ color: #f43434; font-size: 14px; }

.exchange-score .score-info .login-button,.exchange-score .score-info .register_button{ display: block;float: left;width: 78px;height: 28px;line-height: 28px;border: 1px solid #333;text-align: center;}
.exchange-score .score-info .login-button{ margin:0 15px 0 30px;}
.exchange-score .score-info .login-button:hover,.exchange-score .score-info .register_button:hover,.exchange-score .score-info .register_button{ border-color: #f42424;color: #f42424;}


/* 会员内容横向排列样式  */
.dsp-member-top { background-color: #fff2da; height: 106px; border: solid 1px #FED4AE; overflow: hidden;}
.dsp-member-top .dsp-member-info { background-color: #FFF; float: left;}
.dsp-member-top .dsp-member-grade { width: auto; float: left; padding: 18px; border-top: 0; border-left: solid 1px #FED4AE;}
.dsp-member-top .dsp-member-point { float: left; border-top: 0; border-left: solid 1px #FED4AE;}
.dsp-member-top .dsp-member-point dl { width: 119px; margin: 13px 0;}
.dsp-member-top .dsp-memeber-pointcart { float: left; width: 260px; padding: 18px 0; border-top: 0; border-left: solid 1px #FED4AE;}

/*首页内容部分*/
.dsp-main-layout { margin-top: 20px;}
.dsp-main-layout .title { padding: 5px 5px 10px 5px; border-bottom: solid 2px #ff4040;}
.dsp-main-layout .title h3 { font: 18px/32px "microsoft yahei"; color: #333; display: inline-block; *display: inline;}
.dsp-main-layout .title i { background:#fff; display: inline-block; *display: inline; width: 32px; height: 32px; margin-right: 8px; *zoom: 1;}
.dsp-main-layout .title i.iconfont { color: #d93600; font-size: 32px;line-height: 32px; width: 32px;float:left;}

.dsp-main-layout .title .more { float: right; padding: 10px 0 0;}
.dsp-main-layout .title a { color: #999;}
.dsp-main-layout ul { font-size: 0; *word-spacing:-1px/*IE6、7*/;}
.dsp-main-layout li { font-size: 12px; vertical-align: middle; display: inline-block; *display: inline/*IE7*/; *zoom: 1;}

/* 成长值介绍页面 */
.dsp-grade-layout { border: solid 1px #EEE; margin-top: -1px;}
.dsp-grade-layout .title { background-color: #F9F9F9; padding: 9px; border-bottom: solid 1px #EEE;}
.dsp-grade-layout .title h3 { font: 16px/20px "Microsoft Yahei"; color: #333;}
.dsp-grade-layout dl { font-size: 0; *word-spacing:-1px/*IE6、7*/;}
.dsp-grade-layout dt, 
.dsp-grade-layout dd{ font-size: 12px; vertical-align: top; display: inline-block; *display: inline/*IE7*/; *zoom: 1;}
.dsp-grade-layout dt { font-size: 14px; text-align: center; width: 239px; padding: 30px;}
.dsp-grade-layout dt i {display: inline-block; width: 64px; height: 64px;line-height:64px;font-size:36px;color:#fff;background:#ff4040;border-radius: 50%;}
.dsp-grade-layout dd { width: 790px; padding: 30px;}
/* 成长值获得明细 */
.dsp-table-style { width: 100%; margin: 0 auto;}
.dsp-table-style th,
.dsp-table-style td { padding: 12px 0; text-align: center;}
.dsp-table-style thead th { background-color: #F9F9F9; border-bottom: solid 1px #EEE;}
.dsp-table-style tbody td { border-bottom: solid 1px #EEE;}

/*店铺代金券列表*/
.coupon_inner {  position: relative;  padding: 0 20px; }
.coupon .slider_indicators {bottom: -26px; }
.coupon_list {height: 355px;  overflow: hidden; }
.coupon_item {  position: relative;  -moz-border-radius: 6px;       border-radius: 6px;  overflow: hidden;  background: #f6f6f6;margin:20px 10px;margin-bottom: 0 }
.coupon_item:nth-child(2n) {  background: #eeefed; }
.coupon_item:before,
.coupon_item:after {  content: '';  display: block;  position: absolute;  width: 16px;  height: 16px;  right: 54px;  -moz-border-radius: 50%;       border-radius: 50%;  background: #fff; }
.coupon_item:before {  top: -8px; }
.coupon_item:after {  bottom: -8px; }
.coupon_lk {  display: block;  height: 118px;  overflow: hidden;width:380px }
.coupon_img {  overflow: hidden;float: left;  margin: 14px 0 0 10px;  width: 70px;  height: 70px;  padding: 10px;  -moz-border-radius: 50%;       border-radius: 50%;  -webkit-transition: opacity ease .2s;  -o-transition: opacity ease .2s;  -moz-transition: opacity ease .2s;  transition: opacity ease .2s;  background: #fff;  transition: opacity ease .2s; }
.coupon_img img{width:100%;height:100%}
.coupon_lk:hover .coupon_img {  opacity: 0.8; }
.coupon_info {  padding-top: 15px;  margin: 0 72px 0 110px; width:198px;overflow: hidden;}
.coupon_price {  font-family: 'impact';  display: block;  height: 38px;  line-height: 38px; }
.coupon_price i {  font-size: 16px; }
.coupon_price span {  font-size: 34px; }
.coupon_desc { overflow: hidden;width:240px;text-overflow:ellipsis; color: #767b77;  font-size: 14px;  line-height: 24px;  height: 24px;  overflow: hidden;  white-space: nowrap;  -o-text-overflow: ellipsis;     text-overflow: ellipsis;  white-space: nowrap;  overflow: hidden;  text-overflow: ellipsis; }
.coupon_limit {  color: #b1b3b0;  font-size: 14px;  line-height: 24px;  height: 24px;  overflow: hidden;  white-space: nowrap;  -o-text-overflow: ellipsis;     text-overflow: ellipsis;  white-space: nowrap;  overflow: hidden;  text-overflow: ellipsis; }
.coupon_more {  border-left: 1px dashed #f6f6f6;float: right;  height: 100%;  line-height: 118px;  width: 32px;background:#fb0f3a;color:#fff;margin-right: 0;padding-left: 30px;font-size: 12px;   -webkit-transition: color ease .2s;  -o-transition: color ease .2s;  -moz-transition: color ease .2s;  transition: color ease .2s; }
.coupon_more_inner {  display: inline-block;  width: 1px;  line-height: 15px;  vertical-align: middle; }
.dsp-mallvoucher-list .coupon_info{margin-left:25px; width: 270px;}
.o2_mini .coupon_list {  height: 295px; }
.o2_mini .coupon_item:before, .o2_mini .coupon_item:after {  right: 40px; }
.o2_mini .coupon_lk {  height: 98px; }
.o2_mini .coupon_img {  width: 60px;  height: 60px;  margin-top: 10px; }
.o2_mini .coupon_price {  margin-bottom: 0; }
.o2_mini .coupon_desc,
.o2_mini .coupon_limit {  height: 20px;  line-height: 20px; }
.o2_mini .coupon_info {  padding-top: 10px;  margin: 0 60px 0 100px; }
.o2_mini .coupon_more {  height: 98px;  line-height: 98px; }
.o2_mini .coupon_more_inner {  line-height: 13px; }
.o2_mini .coupon .slider_indicators {  bottom: -24px; }
.mod_price{font-size:14px;color:#e33333}
/* 领取店铺代金券 */
.dsp-voucher-exchange { padding: 40px 20px 80px 20px;}
.dsp-voucher-exchange .pic { vertical-align: top; display: inline-block; width: 64px; height: 64px;}
.dsp-voucher-exchange .pic span { line-height: 0; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 64px; height: 64px; overflow: hidden;}
.dsp-voucher-exchange .pic img { max-width: 64px; max-height: 64px; margin-top:expression(64-this.height/2); }
.dsp-voucher-exchange dl { vertical-align: top; display: inline-block; margin-left:20px;}
.dsp-voucher-exchange dt { font: 400 18px/25px "microsoft yahei"; color: #ff4040;}
.dsp-voucher-exchange dt em { font-size: 16px;}
.dsp-voucher-exchange dd { line-height: 24px; color: #999;}
.dsp-voucher-exchange .button { margin-left: 80px; margin-top: 10px;}
.dsp-voucher-exchange .submit { font: normal 14px/28px "Microsoft Yahei"; color: #777; background-color: #F5F5F5; text-align: center; vertical-align: top; display: inline-block; width: 60px; height: 30px; border: solid 1px; border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; cursor: pointer;}

/*积分礼品列表*/
.dsp-exchange-list { padding: 15px;}
.dsp-exchange-list li { transition: all .3s;width: 204px; padding: 15px;}
.dsp-exchange-list li:hover { -webkit-box-shadow: 0 0 10px 0 rgba(0,0,0,0.15); -moz-box-shadow: 0 0 10px 0 rgba(0,0,0,0.15); box-shadow: 0 0 10px 0 rgba(0,0,0,0.15); -webkit-transform: translate3d(0,-3px,0); -moz-transform: translate3d(0,-3px,0); transform: translate3d(0,-3px,0); }
.dsp-exchange-list li .gift-pic { display: block; }
.dsp-exchange-list li .gift-pic a {line-height: 0; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 204px; height: 204px; overflow: hidden;}
.dsp-exchange-list li .gift-pic img { max-width: 204px; max-height: 204px; margin-top:expression(200-this.height/2); }
.dsp-exchange-list li .gift-name { line-height: 18px; display: block; width: 100%;  margin: 10px 0; overflow: hidden;font-size:14px;text-overflow:ellipsis;white-space: nowrap}
.dsp-exchange-list li .gift-name a{color:#555}
.dsp-exchange-list li .ex-btn{ display: block; height: 30px; line-height: 30px; font-size: 14px; color: #fff; text-align: center; background: #f43434; opacity: 0; filter:alpha(opacity=0); -webkit-transition: all .3s; -moz-transition: all .3s; transition: all .3s;}
.dsp-exchange-list li:hover .ex-btn{ opacity: 1; filter:alpha(opacity=100);}
.dsp-exchange-list li .sale-num{color:#8c8c8c;margin:5px 0 10px 0}
.dsp-exchange-list li .sale-num .iconfont{margin-right:5px;font-size:14px}
.dsp-exchange-list li .exchange-rule { overflow: hidden;}
.dsp-exchange-list li .exchange-rule .pgoods-price { line-height: 18px; color: #999; float: right;}
.dsp-exchange-list li .exchange-rule .pgoods-price em { text-decoration: line-through;}
.dsp-exchange-list li .exchange-rule .pgoods-points { font-size: 16px; color: #ff4040;}
.dsp-exchange-list li .exchange-rule .pgoods-grade { font-family: Georgia,Arial; font-size: 18px; line-height: 53px; background-color: #E8E8E8; text-align: center; display: block; width: 40px; height: 53px; float: right; clear: left;}

/*账户信息*/

.dsp-member-account { font-size: 0; *word-spacing:-1px/*IE6、7*/; vertical-align: top; display: inline-block;}
.dsp-member-account dl {font-size: 12px; vertical-align: top; display: inline-block; *display: inline/*IE7*/; padding: 0 10px; margin-left: 10px;*zoom: 1;}


/* 优惠券分类选择 */
.dsp-category { border-bottom-width: 2px;}
.dsp-category dl {  *word-spacing:-1px/*IE6、7*/; }
.dsp-category dt, .dsp-category dd {  *display: inline/*IE7*/; *zoom: 1;}
.dsp-category dt {  width: 110px; }
.dsp-category ul {  *word-spacing:-1px/*IE6、7*/;}
.dsp-category li { border:1px solid transparent; *display: inline/*IE7*/;  margin-left: 0px;*zoom: 1;}
.dsp-category li em{ width: 8px; height:14px; margin-left:6px;line-height:20px;position: relative;display: inline-block;font:10px iconfont;}
.dsp-category li em:after{position: absolute;right:0px;top:0px;content: "\e690";color:#DDDDDD}
.dsp-category li em:before{position: absolute;right:0px;top:8px;content: "\e691";color:#DDDDDD}
.dsp-category li em.desc:before{color:#ff4040}
.dsp-category li em.asc:after{color:#ff4040}
.dsp-category li.selected { color: #ff4040; border-color: #ff4040;background: #fff}
.dsp-category li.selected em{color:#fff}


.dsp-category li .text, 
.dsp-category li a { vertical-align: middle;}


.exchange-cate{ background: #F8F8F8; line-height: 40px; font-size: 14px; }
.exchange-cate a{ color: #555; white-space:nowrap;}
.exchange-cate a.curr,
.exchange-cate a:hover{ color: #f43434; }
.exchange-cate a.curr{ font-weight: bold; }
.exchange-cate a:first-child{ margin-left: 5px; }
.exchange-cate .point{ margin-left: 10px; margin-right: 6px; }

/* =========================== */
/* 积分兑换礼品详情页面 -> goods.php */
/* =========================== */

/* 积分礼品信息兑换 */
.dsp-detail { min-height: 400px; margin-bottom: 10px;  position: relative; z-index: 2;}
.dsp-detail .dsp-info { position: absolute; z-index: 2; top: 0; right: 0;}
/**/
/* ====================== */
/* 店铺简介边栏 -> info.php */
/* ====================== */
.dsp-info { width: 200px;float:left}
.dsp-info .title { background-color: #f8f8f8; padding: 8px 10px; border: solid 1px #D2D2D2;}
.dsp-info .title h4 { font: 600 14px/20px "Microsoft Yahei"; color: #555;}
.dsp-info .content { margin-bottom: 20px;border: solid #E6E6E6; border-width: 0 1px 1px;}
.dsp-info .content dl { font-size: 0; *word-spacing:-1px/*IE6、7*/; display: block; padding: 6px 0;}
.dsp-info .content dl dt { font-size: 12px; color: #666; vertical-align: top; letter-spacing: normal; word-spacing: normal; text-align: right; display: inline-block; *display: inline/*IE7*/; width: 68px; *zoom:1;}
.dsp-info .content dl dd { font-size: 12px; color: #333; vertical-align: top; letter-spacing: normal; word-spacing: normal; white-space: nowrap; text-overflow: ellipsis; display: inline-block; *display: inline/*IE7*/; width: 130px; *width: 125px; overflow: hidden; zoom: 1;}


.dsp-exchangeNote { padding: 0 10px; overflow: hidden;border: solid #E6E6E6; border-width: 0 1px 1px;}
.dsp-exchangeNote li { padding: 10px 0; margin-top: -1px; border-top: dotted 1px #CCC; overflow: hidden;}
.dsp-exchangeNote li .user-avatar { width: 36px; height: 36px; padding: 1px; margin-right: 5px; border: solid 1px #EEE; border-radius: 20px; float: left;}
.dsp-exchangeNote li .user-avatar img { width: 36px; height: 36px; border-radius: 20px;}
.dsp-exchangeNote li .user-name { float: left; width: 130px; height: 20px;}
.dsp-exchangeNote li .user-log { color: #999; float: left; width: 130px; height: 20px;}


/* 礼品图片*/
.dsp-gift-picture { border:1px solid #ddd;background-color: #FFF; width: 400px; height: 400px; position: absolute; z-index:99; top: 0; left: 0;}
.dsp-gift-picture a { line-height: 0; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 400px; height: 400px; overflow: hidden;}
.dsp-gift-picture a img { max-width: 400px; max-height: 400px; margin-top:expression(320-this.height/2); }
/* 站外分享操作 */
.dsp-share { width: 350px; height: 24px; padding-top: 10px; border-top: dotted 1px #EEE; position: absolute; z-index: 1; left: 10px; bottom: 10px;}
.dsp-gift-summary { float: right; width: 600px; min-height: 400px; margin-right: 170px; }
.dsp-gift-summary .name { margin-bottom: 10px}
.dsp-gift-summary .name h1, 
.dsp-gift-summary .name strong { font: 700 16px/1.5 "Microsoft Yahei", Arial; color: #555; text-overflow: ellipsis; white-space: nowrap; display: block; overflow: hidden;} 
.dsp-gift-summary .name strong { font-weight: normal; font-size: 14px; color: #ff4040;}

/* 销售信息 */
.dsp-meta { padding-bottom:12px;background: #f3f3f3; position: relative; z-index: 1; }
.meta-title{margin-bottom:20px;font-size: 18px;color:#fff; height:40px; line-height:40px; padding:0 20px; position:relative; overflow:hidden; z-index:1; background:#f42424;}
.dsp-key {padding-top: 15px; }
.dsp-meta dl, .dsp-key dl { font-size: 0; *word-spacing:-1px/*IE6、7*/;}
.dsp-meta dl dt, .dsp-meta dl dd,
.dsp-key dl dt, .dsp-key dl dd { font-size: 12px; line-height: 20px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/* IE6,7 */; min-height: 20px; padding: 4px 0; *zoom:1;}
.dsp-meta dl dt, .dsp-key dl dt {  float:left; padding-left:20px; width:50px; margin-right:10px; line-height:24px; color:#8c8c8c;}
.dsp-meta dl dd, .dsp-key dl dd { float:left;line-height:24px; width:520px;}
.dsp-meta dl dd i.ver-line { display: inline-block; *display: inline; zoom: 1;}
.dsp-meta dl dd a { color: #005AA0; vertical-align: top; display: inline-block; *display: inline/*IE7*/; *zoom: 1;}
.dsp-meta dl dd a:hover { text-decoration: underline;}
.dsp-meta .points strong  { font-family:"Microsoft Yahei", Arial; font-size: 20px; font-weight:600; color: #ff4040;}
.dsp-meta .points span { font: 600 12px/16px Georgia,Arial; color: #FFF; background-color: #ff4040; padding: 2px 4px; border-radius: 2px;}
.dsp-meta .points em { color: #999; vertical-align: middle; margin-left: 8px;}
.dsp-meta .countdown i { font-family: Georgia,Arial; font-size: 16px; color: #ff4040;}
.dsp-meta .cost-price strong  { text-decoration: line-through;font-weight: normal}
.dsp-meta .promotion-info { color: #ff4040}
.dsp-meta .promotion-info em { color: #690; vertical-align: middle; margin: 2px 0 12px 0;}
.dsp-meta .promotion-info span { color: #999; vertical-align: middle;}
/*购买数量和库存*/
.dsp-figure-input { position: relative; z-index: 1;}
.dsp-figure-input span { margin-left: 10px; color:#999;}
.dsp-figure-input span em { margin: 0 2px;}
.dsp-figure-input span strong { color: #F60; margin: 0 2px;}

/* 立即购买和加入购物车按钮 */
.dsp-gift-summary .dss-btn { display: inline-block; margin: 10px 0 0 80px; position: relative; z-index: 70;}
.dsp-gift-summary .dss-btn { *display: inline/*IE6,7*/; zoom: 1;}
.dsp-gift-summary .dss-btn a.addcart,
.dsp-gift-summary .dss-btn a.no-buynow,
.dsp-gift-summary .dss-btn a.no-addcart { font: lighter 16px/20px "Microsoft Yahei"; color: #FFF; text-align: center; display: inline-block; height: 20px; padding: 6px 18px; margin-right: 10px; border-radius: 3px; position: relative; overflow: hidden; box-shadow: 0 0 0 2px rgba(204,204,204,0.25);}
.dsp-gift-summary .dss-btn a.buynow:hover,
.dsp-gift-summary .dss-btn a.addcart:hover,
.dsp-gift-summary .dss-btn a.no-buynow:hover,
.dsp-gift-summary .dss-btn a.no-addcart:hover  { text-decoration: none;}
.dsp-gift-summary .dss-btn a.buynow { background: #f42424; height:40px; line-height:40px; float:left; margin-right:10px; font-size:18px; color:#fff; padding:0 25px; cursor:pointer; border:0; margin-bottom:10px;}
.dsp-gift-summary .dss-btn a.buynow:hover { background-color:#ec5151}
.dsp-gift-summary .dss-btn a.addcart { background-color: #ff4040;}
.dsp-gift-summary .dss-btn a:hover.addcart { background-color: #BB0000;}
.dsp-gift-summary .dss-btn a.no-buynow, 
.dsp-gift-summary .dss-btn a.no-addcart,
.dsp-gift-summary .dss-btn a:hover.no-buynow, 
.dsp-gift-summary .dss-btn a:hover.no-addcart { background-color: #AAA; cursor: not-allowed;}
.dsp-gift-summary .dss-btn a i { font-size: 17px; margin-right: 6px;}


.dsp-grade {}
.dsp-grade .title { background-color: #F9F9F9; padding: 9px; border-bottom: solid 1px #EEE;}
.dsp-grade .title h3 { font: normal 16px/20px "Microsoft Yahei"; color: #333; }
.dsp-gradeall-bar { font-size: 0; *word-spacing:-1px/*IE6、7*/;  height: 18px; padding: 120px 40px 90px 40px;background:#f2f2f2;display: flex;}
.dsp-gradeall-bar .itemlevel { flex:1;font-size: 12px; line-height: 20px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/* IE6,7 */;  *zoom:1; position: relative; z-index: 1;background:orange;}
.dsp-gradeall-bar .itemlevel:first-child{border-top-left-radius:10px;border-bottom-left-radius:10px;}
.dsp-gradeall-bar .itemlevel:last-child{border-top-right-radius:10px;border-bottom-right-radius:10px;}

.dsp-gradeall-bar .gradelabel { position: absolute; z-index: 1; top: -55px;left:7px;}
.dsp-gradeall-bar .gradelabel strong { font: 18px/20px "Times New Roman", Times, serif; color: #333; display: block;}
.dsp-gradeall-bar .gradelabel i { font: 11px/18px Tahoma, Geneva, sans-serif; color: #555;  display: block;}
.dsp-gradeall-bar .exp-lv1 .gradelabel {}
.dsp-gradeall-bar .exp-lv2 .gradelabel {}
.dsp-gradeall-bar .exp-lv3 .gradelabel {}
.dsp-gradeall-bar .exp-lv4 .gradelabel {}
.dsp-gradeall-bar .bar i { background-color:#F66; display: block; width: 14px; height: 14px; border: solid 2px #FFF; border-radius: 9px; position: absolute; z-index: 3; top: 0;}
.dsp-gradeall-bar .exp-lv1 .bar i { left: 5px;}
.dsp-gradeall-bar .exp-lv2 .bar i { left: 5px;}
.dsp-gradeall-bar .exp-lv3 .bar i { left: 5px;}
.dsp-gradeall-bar .exp-lv4 .bar i { left: 5px;}
.dsp-gradeall-bar .arrow { font-size: 0; border-color: transparent transparent #FFF transparent; border-style: dashed dashed solid dashed; border-width: 8px; width: 0; height: 0; line-height: 0; position: absolute; z-index: 4; top: 20px; left: 5px;}
.dsp-gradeall-bar .exp-lv1 .arrow { left: 5px;}
.dsp-gradeall-bar .exp-lv2 .arrow { left: 5px;}
.dsp-gradeall-bar .exp-lv3 .arrow { left: 5px;}
.dsp-gradeall-bar .exp-lv4 .arrow { left: 5px;}
.dsp-gradeall-bar .tips { background-color: #FFF; border-radius: 5px; padding: 0 10px; white-space: nowrap; position: absolute; z-index: 2; top: 36px; overflow: hidden;}
.dsp-gradeall-bar .tips p { color: #777; padding: 5px; border-top: dotted 1px #CCC;  margin-top: -1px;}
.dsp-gradeall-bar .tips strong { color: #F60;}
.dsp-gradeall-bar .tips em { color: #333;}
.dsp-gradeall-bar .exp-lv1 .tips  { left: -20px;}
.dsp-gradeall-bar .exp-lv2 .tips  { left: -120px;}
.dsp-gradeall-bar .exp-lv3 .tips  { left: -120px;}
.dsp-gradeall-bar .exp-lv4 .tips  { left: -120px;}

.dsp-sidebar { width:140px; float: left;}
.dsp-sidebar-container { margin-bottom: 10px;}
.dsp-sidebar-container .title{ text-align:center; height:20px; line-height:20px; position:relative; margin-bottom:10px;}
.dsp-sidebar-container .title h3{ font-size:14px; padding:0 10px; background-color:#fff; position:relative; z-index:2; display:inline-block; color:#8c8c8c;}
.dsp-sidebar-container .title span{ position: absolute;z-index: 1;left: 0;right: 0;top: 9px;height: 1px; border-bottom:1px dashed #e5e5e5;}


.dsp-sidebar .recommend { width: 140px; margin: 0 auto; overflow: hidden;}
.dsp-sidebar .recommend li {width:140px;height:160px;margin-bottom:5px;position: relative}
.dsp-sidebar .recommend .gift-pic { width: 140px; height: 140px; margin: 0 auto;}
.dsp-sidebar .recommend .gift-pic a { line-height: 0; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 140px; height: 140px; overflow: hidden;}
.dsp-sidebar .recommend .gift-pic a img { max-width: 140px; max-height: 140px; margin-top:expression(160-this.height/2); }
.dsp-sidebar .recommend .gift-name {  width:100%; height:20px; line-height:20px; text-align:center; color:#f42424;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
.dsp-sidebar .recommend .gift-name a{color:#f42424;}
.dsp-sidebar .track-more { text-align:center; font-size:0; height:24px; line-height:24px; margin-top:10px;}
.dsp-sidebar .track-more .sprite-up{ margin-right:20px;}
.dsp-sidebar .track-more .iconfont{ font-size:24px; color:#8c8c8c;}
.dsp-sidebar .track-more a:hover .iconfont{ color:#f42424;}
.dsp-sidebar .recommend .pgoods-points { font: 600 14px/20px Tahoma, Geneva, sans-serif; color: #ff4040; margin-right: 6px;}
.dsp-sidebar .recommend .pgoods-points span { font: 600 12px/16px Georgia,Arial; color: #FFF; background-color: #ff4040; padding: 2px 4px; border-radius: 2px; }
.dsp-goods-main { float: right; width: 970px; }
.dsp-goods-layout { width: 100%; margin-bottom: 10px; position: relative; z-index: 1; overflow: hidden;}
/* 商品内容处TabBar */
.tabbar { background: #FFF;}
.dsp-goods-title-nav { margin-top: 7px;}
.dsp-goods-title-nav ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; background-color: #FCFCFC; border: solid #D7D7D7 1px;}
.dsp-goods-title-nav ul li { vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block;}
.dsp-goods-title-nav ul li { *display: inline/*IE6,7*/;}
.dsp-goods-title-nav ul li a { font: normal 14px/20px "Microsoft Yahei"; text-decoration:none; color:#777; background-color: #F5F5F5; display: block; padding: 6px 15px 5px 15px; border-style: solid; border-color: #D7D7D7; border-width: 0 1px 0 0;}
.dsp-goods-title-nav ul li.current { margin: -7px 0 -1px -1px;}
.dsp-goods-title-nav ul li.current a { color: #333; background-color: #FFF; padding: 11px 15px 6px 15px; border-style: solid; border-color: #ff4040 #DDD transparent #DDD; border-width: 2px 1px 0 1px ;}
.dsp-goods-info-content { padding: 10px 0; border: 0 none; margin: 0; overflow: hidden;}

/*平台代金券*/
.coupon_info.mallvoucher{margin:0 72px 0 35px;display:flex;width: 260px;}
.coupon_info.mallvoucher .info{width: 140px;}
.coupon_info.mallvoucher .range{width: 120px;font-size:14px}
.coupon_info.mallvoucher .range div{padding:5px 0}















.breadBox {
    border-bottom: none;
}

.main {
    margin-left: 15%;
    width: 70%;
}

.userInfoBox {
    margin-bottom: 1vw;
    height: 70vh;
    display: flex;
    /* border: 1px solid ; */
}
aside {
    padding: 10px 0px;
    width: 15%;
    height: fit-content;
    border: 1px solid #E2E3E9;
    /* background-color: red; */
}

aside>div:nth-child(1) {
    font-size: 20px;
    padding-top: 10px;
    text-indent: 15%;
    font-weight: 550;
}

aside>div:not(:first-child) {
    margin-bottom: .5vw;
    padding: 7px 0vw;
    font-size: 15px;
    display: flex;
    justify-content: left;
    place-items: center;
    width: 100%;
    cursor: pointer;
}

aside>div>div:nth-child(1) {
    margin-right: .5vw;
    margin-left: 15%;
    font-size: 20px;
}

._line {
    width: 80% !important;
    height: 0px !important;
    border: none;
    border-bottom: 1px solid var(--line);
    margin: 0px 0px .7vw 10% !important;
}

aside>div:not(.bgSelect):hover {
    color: var(--blue-deep);
}

.content {
    width: 80%;
    display: block;
    padding: 0vw 1vw 1vw 1vw;
}

/*  */
.titleBox1 {
    margin-left: 3%;
    font-size: .8vw;
    color: var(--text-color2);
    cursor: pointer;
    display: flex;
}
.titleBox1>div{
    padding-bottom: .2vw;
}

.optionsBox>.titleSelect {
    color: var(--text-color) !important;
}

.optionsBox>.titleSelect::before {
    width: 100%;
    background-color: var(--text-color) !important;
}
.addrBox{
    margin-top: 1vw;
    width: 80%;
    color: var(--text-color);
    display: flex;
}
.card{
    padding: 1vw;
    width: 40%;
    font-size: .75vw;
    margin-left: 5%;
    border-radius: 3px;
    box-sizing: border-box;
    border: 1px solid var(--text-color4);
}
.card[data-select="true"]>div:nth-child(3)>span:first-child{
    color: var(--blue-deep);
}
.card>div:nth-child(2){
    margin-top: .5vw;
}
.defaultBox{
    border: 1px solid var(--blue-deep);
    box-shadow: -4px -4px 8px 0px rgba(0,0,0,0.1),
     4px 4px 8px 0px rgba(0,0,0,0.1);
}
.icon-location{
    font-size: 1vw;
}

.pageBox {
    width: 100%;
    background-color: #F5F5F5;
}

.logo {
    width: 100%;
    padding: 1vw 0px;
    background-color: white;
}

.main {
    margin-left: 20%;
    padding: 1vw 0px 2vw 0px;
    width: 60%;
    /* height: 70vh; */
}
.contentBox {
    margin-bottom: 50px;
    padding: 10px;
    background-color: white;
}

.goodsInformation>div {
    margin-top: .2vw;
    display: flex;
}

.tdTitle {
    color: var(--text-color) !important;
    font-size: 0.9vw;
    margin-bottom: 0.4vw;
}

.name {
    margin-left: auto;
    width: 70%;
    display: block;
    color: var(--blue-deep);
    /* border: 1px solid red; */
}

.imageBox {
    width: 8vw;
}

.titleBox {
    display: flex;
    justify-content: center;
    text-align: left;
    background-color: #F5F5F5;
    padding: 10px;
}

.titleBox>div {
    width: 25%;
}
.titleBox>div>div:first-child{
    color: var(--text-color2);
    margin-bottom: 5px;
}
.layui-timeline{
    margin: 20px 0px 0px 20px;
    padding: 10px;
    border-bottom: 1px solid var(--line);
}
.shou{
    padding: 7px 10px;
    font-size: 20px;
    background-color: var(--blue-deep);
    color: white;
    border-radius: 50%;
    margin-right: 10px;
}
.othersBox>div{
    margin-left: 20px;
}

.breadBox {
    border-bottom: none;
}

.main {
    margin-left: 15%;
    width: 70%;
}

.userInfoBox {
    margin-bottom: 1vw;
    height: 70vh;
    display: flex;
    /* border: 1px solid ; */
}

aside {
    padding: 10px 0px;
    width: 15%;
    min-width: 200px;
    height: fit-content;
    border: 1px solid #E2E3E9;
    /* background-color: red; */
}

aside>div:nth-child(1) {
    font-size: 20px;
    padding-top: 10px;
    text-indent: 15%;
    font-weight: 550;
}

aside>a:not(:first-child) {
    margin-bottom: .5vw;
    padding: 7px 0vw;
    font-size: 15px;
    display: flex;
    justify-content: left;
    place-items: center;
    width: 100%;
    cursor: pointer;
}
aside>a>div:nth-child(1) {
    margin-right: .5vw;
    margin-left: 15%;
    font-size: 20px;
}
._line{
    width: 80% !important;
    height: 0px !important;
    border: none;
    border-bottom: 1px solid var(--line);
    margin: 0px 0px .7vw 10% !important;
}
aside>a:not(.bgSelect):hover{
    color:  var(--blue-deep);
}
.content {
    width: 80%;
    display: block;
    padding: 0vw 1vw 1vw 1vw;
}


.userInfo{
    margin-top: 2vw;
    width: 50%;
    display: flex;
    flex-direction: column;
    place-items: baseline;
    font-size: 16px;
}
.userInfo>div{
    margin-bottom: 1.5vw;
}
.label{
    width: 130px;
    padding-right: .5vw;
    /* border: 2px solid ; */
    text-align: right;
}
.save{
    margin-top: 2vw;
    width: 5vw;
}
.input2{
    width: 300px;
    background-color: #f8f8f8;
    height: 30px;
}
.update{
    color: var(--blue-deep);
    cursor: pointer;
}
.update:hover{
    color: red;
}
.userInfo>div>div:nth-child(2)>input{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.text{
    color: var(--text-color2);
}

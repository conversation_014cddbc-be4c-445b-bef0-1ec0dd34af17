<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <link rel="stylesheet" href="../../modules/swiper/basic/css/idangerous.swiper.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/swiper/basic/js/idangerous.swiper.min.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/checkOrder.css">
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
    <script src="../../dialog/editAddr.js"></script>
</head>


<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="header">
            <div class="logo">
                <div>
                    <img src="../../images/icons/hlk.png" class="checkOrderImage">
                </div>
                <div style="width: 1px;background-color: var(--text-color4);padding: 1.5vw 0;margin-left: 10px;"></div>
                <div style="font-size: 1.3vw;color: var(--text-color);padding-left: 20px;">提交订单</div>
            </div>
            <!-- 步骤条 -->
            <div class="stepBoxContainer">
                <div class="stepBox" style="margin-bottom: 30px;">
                    <div class="item flex" style="max-width: fit-content;">
                        <div class="circle">
                            <span class="number">1</span>
                        </div>
                        <div class="stepText">查看购物车</div>
                    </div>
                    <div class="item flex">
                        <div class="stepLine"></div>
                        <div class="circle">
                            <span class="number">2</span>
                        </div>
                        <div class="stepText">核对订单信息</div>
                    </div>
                    <div class="item flex">
                        <div class="stepLine"></div>
                        <div class="circle">
                            <span class="number">3</span>
                        </div>
                        <div class="stepText">订单提交成功</div>
                    </div>
                </div>
            </div>

        </div>
      
        <!-- 主体 -->
        <div class="checkOrderMain">
            <div class="left">
                <!-- 收货信息部分 -->
                <div class="addressBox">
                    <div class="boxTitle">收货人信息</div>
                    <div class="boxTitle2">
                        收货地址 
                        <span class="addAddressBtn" onclick="editAddr({title:'新增收货地址'})"> <i class="iconfont icon-tianjia1"></i> 新增</span>
                     </div>
                    <div class="addressList">
                        <div class="addressItem currentAddr">
                            <div class="flex" style="justify-content: left;">
                                <div>海凌科</div>
                                <div class="phone">15361580137</div>
                                <div class="address">
                                    <div class="textOver">广东省深圳市龙华区民治街道民乐社区星河WORLD E栋大厦17层 1705</div>
                                </div>
                            </div>
                        </div>
                        <div class="addressItem">
                            <div class="flex" style="justify-content: left;">
                                <div>海凌科</div>
                                <div class="phone">15361580137</div>
                                <div class="address">
                                    <div class="textOver">广东省深圳市龙华区民治街道民乐社区星河WORLD E栋大厦17层 1705</div>
                                </div>
                            </div>
                        </div>
                        <div class="addr_more hoverBlue">
                            更多收货地址
                            <i class="iconfont icon-xiangxia"></i>
                        </div>
                    </div>
                    <div class="boxTitle2" style="border-top: 1px solid var(--line);padding-top: 10px;">
                        发票地址 
                        <span class="addAddressBtn" onclick="editAddr({title:'新增发票地址'})"> <i class="iconfont icon-tianjia1"></i> 新增</span> 
                    </div>
                    <div class="addressList">
                        <div class="addressItem currentAddr">
                            <div class="flex" style="justify-content: left;">
                                <div>海凌科</div>
                                <div class="phone">15361580137</div>
                                <div class="address">
                                    <div class="textOver">广东省深圳市龙华区民治街道民乐社区星河WORLD E栋大厦17层 1705</div>
                                </div>
                            </div>
                        </div>
                        <div class="addressItem">
                            <div class="flex" style="justify-content: left;">
                                <div>海凌科</div>
                                <div class="phone">15361580137</div>
                                <div class="address">
                                    <div class="textOver">广东省深圳市龙华区民治街道民乐社区星河WORLD E栋大厦17层 1705</div>
                                </div>
                            </div>
                        </div>
                        <div class="addr_more hoverBlue">
                            更多发票地址
                            <i class="iconfont icon-xiangxia"></i>
                        </div>
                    </div>

                </div>
                <!-- 快递选择部分 -->
                <div class="expressBox">
                    <div class="boxTitle">快递</div>
                    <div class="expressContent">
                        <div class="expressType">
                            <div class="typeTitle">
                                <input type="radio" name="title" checked>
                                <span>使用自己的配送账户</span>
                            </div>
                            <div class="typeDesc">国际快递专线特惠型：<b
                                    style="color: var(--text-color);">EXW</b>（运费、关税、海关和税费由买方承担）</div>
                            <div class="expressOptions">
                                <label class="radioItem">
                                    <input type="radio" name="express" checked>
                                    <span>UPS通单号：(3~7天)</span>
                                </label>
                                <label class="radioItem">
                                    <input type="radio" name="express">
                                    <span>DHL快递-提单号：(3~7天)</span>
                                </label>
                                <label class="radioItem">
                                    <input type="radio" name="express">
                                    <span>联邦快递通函(不先垫单号：(3~7天)</span>
                                </label>
                                <label class="radioItem">
                                    <input type="radio" name="express">
                                    <span>联邦快递通函经济单据号：(4~8天)</span>
                                </label>
                                <label class="radioItem"></label>
                                    <input class="input" id="expressInput" placeholder="*请输入配送账户">
                                </label>
                            </div>
                        </div>

                        <div class="expressType" style="margin-top: 20px;">
                            <div class="typeTitle">使用推荐的配送账户</div>
                            <div class="typeDesc">国际贸易术语解释通则：<b
                                style="color: var(--text-color);">FCA</b>（关税、海关和税费由买方承担)</div>
                            <div class="expressOptions">
                                <label class="radioItem">
                                    <input type="radio" name="express2">
                                    <span>UPS通单号：(3~7天)</span>
                                    <span class="price">¥3.98</span>
                                </label>
                                <label class="radioItem">
                                    <input type="radio" name="express2">
                                    <span>DHL快递-提单号：(3~7天)</span>
                                    <span class="price">¥3.98</span>
                                </label>
                                <label class="radioItem">
                                    <input type="radio" name="express2">
                                    <span>联邦快递通函(不先垫单号：(3~7天))</span>
                                    <span class="price">¥3.98</span>
                                </label>
                                <label class="radioItem">
                                    <input type="radio" name="express2">
                                    <span>联邦快递通函经济单据号：(4~8天)</span>
                                    <span class="price">¥3.98</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 订单表格 -->
                <div class="tablesBox">
                  
                    <table class="layui-table" lay-skin="line" style="background-color: white;margin: 0;">
                        <colgroup>
                            <col width="40%">
                            <col width="12%">
                            <col width="1%">
                            <col width="10%">
                        </colgroup>
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th class="flex" style="justify-content: left;">
                                    <div>海凌科供应商</div>
                                    <div class="iconfont  icon-zaixiankefu1" style="margin-left: 30px;"
                                        onclick="toRouter(this)" data-link="../userInfo/message.html"></div>
                                </th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="goodsInfo textOver" style="place-items: center;">
                                       
                                        <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                            <img src="../../images/icons/device2.png" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                        </div>
                                        <div class="goodsInformation"
                                            style="width: 60%;margin-left: 2%;margin-right: auto;">
                                            <div class="tdTitle"> <span class="textOver">220V转5V3.3V9V12V15V24V</span>
                                            </div>
                                            <div>原厂编号<span class="name textOver">ACDC电源模块</span></div>
                                            <div>制造商: <span class="name textOver">Hi-Link</span> </div>
                                            <div class="textOver">制造商编号: <span
                                                    class="name textOver">727-S40FC008C3B1V000 </span>
                                            </div>
                                            <div class="textOver">
                                                型号:<span class="name textOver">HLK-PM01</span>
                                            </div>
                                            <div class="textOver">
                                                客户编号:<span class="name textOver">220V转5V3.3V9V12V15V24V</span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>单价：¥ 5.66</div>
                                </td>
                                <td>
                                    <!-- 计数器 -->
                                    <div class="computer">
                                        <div class="iconfont icon-jianhao" onclick="compute(0,10,0)"></div>
                                        <div style="flex: 1;" class="count"> 1 </div>
                                        <div class="iconfont icon-tianjia1" onclick="compute(1,10,0)"></div>
                                    </div>
                                </td>
                                <td class="tableBtnBox" rowspan="2" colspan="1"
                                    style="border-left: 1px solid rgb(238, 238, 238);">
                                    <div>合计：<span class="money">￥11.66</span> </div>
                                </td>
                            </tr>
                            <!--  -->
                            <tr>
                                <td>
                                    <div class="goodsInfo textOver" style="place-items: center;">
                                       
                                        <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                            <img src="../../images/icons/device2.png" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                        </div>
                                        <div class="goodsInformation"
                                            style="width: 60%;margin-left: 2%;margin-right: auto;">
                                            <div class="tdTitle"> <span class="textOver">220V转5V3.3V9V12V15V24V</span>
                                            </div>
                                            <div>原厂编号<span class="name textOver">ACDC电源模块</span></div>
                                            <div>制造商: <span class="name textOver">Hi-Link</span> </div>
                                            <div class="textOver">制造商编号: <span
                                                    class="name textOver">727-S40FC008C3B1V000 </span>
                                            </div>
                                            <div class="textOver">
                                                型号:<span class="name textOver">HLK-PM01</span>
                                            </div>
                                            <div class="textOver">
                                                客户编号:<span class="name textOver">220V转5V3.3V9V12V15V24V</span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>单价：¥ 6</div>
                                </td>
                                <td>
                                    <!-- 计数器 -->
                                    <div class="computer">
                                        <div class="iconfont icon-jianhao" onclick="compute(0,10,0)"></div>
                                        <div style="flex: 1;" class="count"> 1 </div>
                                        <div class="iconfont icon-tianjia1" onclick="compute(1,10,0)"></div>
                                    </div>
                                </td>
                            </tr>
                            <!--  -->
                            <tr>
                                <td class="hover" style="text-align: center;" colspan="4">
                                    --- 展开所有订单 ---
                                </td>
                            </tr>
                        </tbody>
                    </table>

                </div>
            </div>
            <div class="right">
                <!-- 侧边栏 -->
                <div class="aside">
                    <div class="asideItem serverInfoIcon" style="color: black;">
                        结算明细
                    </div>
                    <div class="asideItem" style="margin-top: 10px;">
                        <div>商品总价</div>
                        <div>¥ 5.66</div>
                    </div>
                    <div class="asideItem">
                        <div>运费价格</div>
                        <div>¥ 5.66</div>
                    </div>
                    <div class="asideItem"
                        style="font-size: 16px;border-bottom: 1px solid var(--line);padding-bottom: 10px ;">
                        <div style="color: black;">合计</div>
                        <div class="red serverInfoIcon"> <b>¥ 5.66</b> </div>
                    </div>
                    <div>
                        <div>优惠券码</div>
                        <div class="flex coupon">
                            <input type="text">
                            <button class="button_blue">确定</button>
                        </div>
                    </div>
                    <div class="asideItem borderB" style="margin:10px 0 15px 0">
                        <button class="button button_blue" style="width: 95%;margin: 10px auto;border-radius: 45px;"
                            onclick="toRouter(this)" data-link="../orders/payPage.html">提交订单</button>
                    </div>


                    <!--  -->
                    <div class="serverInfo">
                        <div class="serverTitle" style="font-size: 15px;">服务申明</div>
                        <div class="serverTitle flex">
                            <div class="iconfont icon-sign serverInfoIcon"
                                style="margin-left: .2vw;margin-right: .2vw;"></div>
                            <div>快递</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>支持七天无理由退货</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>如果快递丢失，支持退货</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>如果快递损坏，支持退货</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>支持90天内免费换货</div>
                        </div>

                        <div class="serverTitle"> <i class="iconfont icon-secured serverInfoIcon"></i> 安全与隐私</div>
                        <div class="serverItem" style="margin-top: 3px;">
                            <div>安全付款:未经您的同意，我们不会与任何第三方分享您的个人信息。</div>
                            <div style="margin-top: 3px;">安全的个人资料:我们保护您的隐私，确保您的个人资料安全可靠。</div>
                        </div>

                        <div class="serverTitle">
                            <i class="iconfont icon-money-circle serverInfoIcon"></i>
                            支付安全
                        </div>
                        <div class="serverItem" style="margin-top: 3px;">
                            <div class="paymentMethods flex" style="justify-content: left;margin-bottom: .5vw;">
                                <!-- <img src="../../images/icons/zhifubao.png" alt=""> -->
                                <img src="../../images/icons/paypal.png" alt="">
                                <img src="../../images/icons/weixin.png" alt="">
                            </div>
                            <div>与受欢迎的支付合作伙伴合作，您的个人信息是安全的。</div>
                        </div>
                    </div>
                    <!-- 右侧边栏 -->
                  
                    <script>
                        /** 节流函数 */
                        function throttle(func, delay) {
                            let timer = null;
                            return function (...args) {
                                if (timer === null) {
                                    timer = setTimeout(() => {
                                        func.apply(this, args);
                                        timer = null;
                                    }, delay);
                                }
                            };
                        }
                        function isAtFooter() {
                            const footerRect = document.querySelector('.footer').getBoundingClientRect();
                            const asideRect = document.querySelector('.aside').getBoundingClientRect();
                            return asideRect.bottom > footerRect.top && asideRect.top < footerRect.bottom;
                        }
                        function handleScroll() {
                            if (isAtFooter()) {
                                $('.aside').stop(true, true).fadeOut(300);
                            } else {
                                $('.aside').stop(true, true).fadeIn();
                            }
                        }

                        // 节流处理滚动事件
                        // const throttledScroll = throttle(handleScroll, 200);
                        // window.addEventListener('scroll', throttledScroll);
                    </script>
                </div>
            </div>
        </div>
        <div class="bug"></div>
        <!-- 底部开始 -->
        <div class="footer">
            <div class="footer2">
                <div class="footer1Content">
                    <div>深圳市海凌科电子有限公司</div>
                    <div>电话 : 0755-23152658</div>
                    <div>邮箱 : <EMAIL></div>
                    <div>地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A</div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                <div class="footer1Content">
                    <div>公司</div>
                    <div>关于我们</div>
                    <div>新闻中心</div>
                    <div>品质保证</div>
                    <div>提交工单</div>
                    <div>企业社会责任</div>
                    <div></div>
                </div>
                <div class="footer1Content">
                    <div>资源</div>
                    <div>新品速递</div>
                    <div>代理招商</div>
                    <div>应用场景</div>
                    <div>服务和工具</div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                <div class="footer1Content">
                    <div>支持</div>
                    <div>联系我们</div>
                    <div>帮助</div>
                    <div>反馈</div>
                    <div>Cookie政策</div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                <!--  -->
                <div class="footer1Content">
                    <div class="connectOur">联系我们</div>
                    <div class="footerLogo">
                        <img src="../../images/icons/weixin-baidi.png" alt="" style="height: 100%;">
                        <img src="../../images/icons/youjian-baidi.png" alt="" style="height: 100%;">
                        <img src="../../images/icons/wangzhi-landi.png" alt="" style="height: 100%;">
                        <img src="../../images/icons/skype-baidi.png" alt="" style="height: 100%;">
                    </div>
                    <div><img src="../../images/icons/gzh.png" alt="" style="width: 5vw;max-width: 75px;"></div>
                    <div>关注微信订阅号</div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            <div class="footer3">
                联系电话 : 0755-23152658 版权所有 : 深圳市海凌科电子有限公司 备案号 : 粤ICP备12055399号-1 技术支持 : 极思灵创
            </div>
        </div>

        <!-- 底部结束 -->

        <script>
            function next(step = 2) {
                // 01 当前步骤
                const list = $('.item');
                list.each(function (index, item) {
                    if (index < step) {
                        item.classList.add('current')
                    }
                })
                // 02 当前步骤对应的内容
                const stepList = $('[data-step]');
                stepList.each(function (index, item) {
                    if (item.getAttribute('data-step') != step || !item.getAttribute('data-step').includes(step)) {
                        item.style.display = 'none';
                    }
                })

            }
            next(2)
        </script>
    </div>

</body>

</html>
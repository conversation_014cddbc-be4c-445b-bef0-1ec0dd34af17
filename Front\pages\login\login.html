<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../script/index.js"></script>
    <script src="../../modules/crypto.js"></script>
</head>
<style>
    .pageBox{
        position: relative;
        /* background-color: #F1F5FA; */
        width: 100%;
        height: 100vh;
        top: 0;
        left: 0;
    }
    .loginBg{
        position: absolute;
        top: 0;
        /* left: -100px; */
        width: 100%;
        height: 100%;
        z-index: 0;
        object-fit: cover;
    }
    .logo{
        position: absolute;
        top: 6%;
        left: 8%;
        z-index: 0;
        object-fit: contain;
    }
    .loginBox{
        position: absolute;
        right: 20%;
        top: 20%;
        width: 400px;
        height: 500px;
        background-color: white;
        border-radius: 5px;
        padding: 25px 0px;
        box-shadow: 4px 4px 16px 0px rgba(0,0,0,0.05), -4px -4px 16px 0px rgba(0,0,0,0.05);
        border: 1px solid #E2E3E9;
    }
    .loginItem{
        width: 100%;
        padding: 20px 0px;
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .title{
        position: relative;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #2E2E2E;
        line-height: 33px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
    .title:before{
        position: absolute;
        content: "";
        left: 25%;
        width: 50%;
        height: 3px;
        background-color: var(--blue-deep);
        bottom: -10px;
    }
    .input{
        width: 70%;
        border-radius: 0px;
        border: 1px solid var(--line);
        background-color: var(--line);
    }
    .button{
        width: 77.5%;
        padding: 11px;
        background-color: var(--blue-deep);
        color: white;
        border-radius: 3px;
        border: 1px solid var(--blue-deep);
        letter-spacing: 4px;
    }
    .rememberBox{
        justify-content: left;
        padding: 5px 0px 0px 0px;
        color: var(--text-color);
        font-size: 14px;
        margin-bottom: 10px;
    }
    .otherBox{
        color: var(--text-color);
        font-size: 14px;
    }
    .otherBox>div{
        padding: 0px 10px;
        cursor: pointer;
        user-select: none;
    }
    .otherBox>div:hover{
        color: var(--blue-deep);
    }
    .line{
        margin-top: 1px;
        height: 18px;
        background: var(--text-color);;
        margin-left: 0px;
    }
    .loginLine{
        margin-top: 2px;
        background-color:  #D9D9D9;
        width: 10px;
        height: 1px;
    }
    .input:focus{
        border:1px solid #2C79E8 !important;
    }
    .logoBox{
        padding: 0px;
        height: 40px;
    }
    .logoBox>img{
        cursor: pointer;
        user-select: none;
    }

    /* 隐藏原生小眼睛 */
    input[type="password"]::-ms-reveal{
        display: none;
    }

    /* input[type="password"]{
        background-image: url('../../images/icons/hlk.png');
        background-repeat: no-repeat;
        background-position: right center;
        padding-right: 20px; 
    } */

    .eyesbox{
        position: absolute;
        width: 12%;
        height: 60%;
        /* background-color: rgba(206, 217, 228, 0.671); */
        right: 10%;
        filter: drop-shadow(0 0 4px #ccc);
    }
   
    /** 密码框的小眼睛 纯css控制 */
    #eyeInput[type="checkbox"]:checked ~ .close {
        display: block;
    }

    #eyeInput[type="checkbox"]:checked ~ .show {
        display: none;
    }

    #eyeInput[checked] ~ .close {
        display: none;
    }

    #eyeInput[checked] ~ .show {
        display: block;
    }

    .layui-unselect{
        min-width: calc(80% - 8px);
        background-color: var(--bd);
    }
    .eyesbox>.layui-form-checkbox{
        display: none;
    }


</style>
<body>
    <div class="pageBox">
        <img src="../../images/icons/denglu-bg.png" class="loginBg">
        <img src="../../images/icons/hlk.png" class="logo">
        <form class="loginBox layui-form">
            <div class="loginItem">
                <div class="title">邮箱登录</div>
            </div>
            <div class="loginItem">
                <input class="input" type="text" name="email" placeholder="请输入邮箱" lay-verify="required"
                 lay-reqtext="请输入正确的邮箱" lay-vertype="tips">
            </div>
            <!-- 密码 -->
            <div class="loginItem" style="position: relative;padding-top: 5px;">
                <input class="input" id="pwd" type="password" name="password" placeholder="请输入密码" lay-verify="required|password">
                <div class="eyesbox flex" id="eyesbox">
                    <input type="checkbox" id="eyeInput" class="changeInputType" checked onchange="changeInputType(this)">
                    <label for="eyeInput" class="close">
                        <i class="iconfont icon-denglu-mimabukejian"
                        style="font-size: 23px;color: var(--text-color);"></i>
                    </label>

                    <label for="eyeInput" class="show">
                        <i class="iconfont icon-denglu-mimakejian"
                        style="font-size: 23px;color: var(--text-color);"></i>
                    </label>
                </div>
            </div>
            <div class="loginItem rememberBox" style="padding-left:12%;">
                <input type="checkbox" name="remember" id="remember">
                <label for="remember" style="margin: -2px 5px;">记住密码</label>
            </div>
            <div class="loginItem">
                <button class="button" lay-submit lay-filter="demo1">登录</button>
            </div>
            <div class="loginItem otherBox">
                <div style="color: #E72A19;" onclick="toForgetPwd()">忘记密码</div>
                <span class="line"></span>
                <div onclick="toRegister()">注册新账号</div>
            </div>
            <div class="loginItem flex" style="color: var(--text-color);font-size: 13px;">
                <div class="loginLine"></div>
                <div class="loginLine"></div>
                <div class="loginLine"></div>
                <div style="padding: 0 10px;"> 其它登录方式 </div>
                <div class="loginLine"></div>
                <div class="loginLine"></div>
                <div class="loginLine"></div>
            </div>
            <div class="loginItem logoBox">
               <img src="../../images//icons/google.png" alt="Google" style="margin-right: 20px;">
               <img src="../../images//icons/Facebook.png" alt="Facebook">
            </div>
        </form>
    </div>
    <script>
        var $ = layui.$
        var form = layui.form
        // 改变密码演示与否
        var eyeInput = document.getElementById("eyeInput");
        var pwdInput = document.getElementsByName("password");

        // 自定义验证规则
        // form.verify({
        //     password: function(value) {
        //         // console.log(value);
        //         // if (value.length < 6) {
        //         //     return '密码长度不能小于6位';
        //         // }
        //         // return 
        //     },
        // });
        /** 计算hash  */
        const generateHash =  function (str) {
                const hash = CryptoJS.MD5(str).toString();
                console.log(hash);
                return hash;
        };
        generateHash('123456')
        // 提交事件
        form.on('submit(demo1)', function(data){
            const formData = data.field
            formData.password = generateHash(formData.password);
            console.log(formData);
            return false; // 阻止默认 form 跳转
        });

        const changeInputType = (e) => {
            var show = e.checked
            $('#pwd').attr("type", !show ? "text" : "password");
        }
        const toRegister = () =>{
            window.localStorage.setItem('a',true)
            window.location.href = "./register.html";
        }
        const toForgetPwd = () =>{
            window.localStorage.setItem('a',true)
            window.location.href = "./forgetPwd.html";
        }
        window.localStorage.removeItem('b')
    </script>
</body>
</html>
/* <!-- 用户中心 --> */
.breadBox {
    border-bottom: none;
}
.main {
    margin-left: 15%;
    width: 70%;
}

.userInfoBox {
    margin-bottom: 1vw;
    /* height: 70vh; */
    display: flex;
    /* border: 1px solid ; */
}

aside {
    padding: 10px 0px;
    width: 15%;
    height: fit-content;
    border: 1px solid #E2E3E9;
    /* background-color: red; */
}

aside>div:nth-child(1) {
    font-size: 20px;
    padding-top: 10px;
    text-indent: 15%;
    font-weight: 550;
}

aside>div:not(:first-child) {
    margin-bottom: .5vw;
    padding: 7px 0vw;
    font-size: 15px;
    display: flex;
    justify-content: left;
    place-items: center;
    width: 100%;
    cursor: pointer;
}

aside>div>div:nth-child(1) {
    margin-right: .5vw;
    margin-left: 15%;
    font-size: 20px;
}

._line {
    width: 80% !important;
    height: 0px !important;
    border: none;
    border-bottom: 1px solid var(--line);
    margin: 0px 0px .7vw 10% !important;
}

aside>div:not(.bgSelect):hover {
    color: var(--blue-deep);
}

/* <!-- 过滤功能 --> */
.content {
    padding: 0px 1vw 1vw 1vw;
    /* height: 100vh; */
    width: 80%;
    /* border: 1px solid ;  */
    overflow-y: auto;
}

.filterBox {
    position: sticky;
    top: 0;
    background: white !important;
    display: flex;
    place-items: center;
    /* border: 1px solid ;  */
    z-index: 2;
}

.filterBox>.flex {
    margin: 0px 1vw;
    width: 30%;
    /* border: 1px solid ; */
}

/* label */
.filterBox>div>.label {
    width: fit-content;
    white-space: nowrap;
    margin-right: .5vw;
    font-size: .8vw;
}


/* <!-- 所有订单-表格 --> */
.tablesBox {
    padding: 1vw 0px;
    width: 100%;
    /* height: 100vh; */
    /* border: 1px solid ; */
    overflow: auto;
    box-sizing: border-box;
}

.layui-table:not(:first-child) {
    margin-top: 2vw;
}

th {
    border: none !important;
    font-weight: 500 !important;
    font-size: .8vw !important;
}

.goodsInfo {
    min-width: 15vw;
    display: flex;
    /* border: 1px solid red; */
}

.goodsInfo>div:nth-child(2) {
    width: 100%;
    object-fit: fill;
    text-align: center;
}

.goodsInfo>div:nth-child(3)>div {
    color: var(--text-color2);
}

.goodsInformation>div {
    margin-top: .2vw;
    display: flex;
}

.name {
    margin-left: auto;
    width: 60%;
    display: block;
    text-align: left;
    color: var(--text-color);
    /* border: 1px solid red; */
}

.tdTitle {
    color: var(--text-color) !important;
    font-size: 0.9vw;
    margin-bottom: 0.4vw;
}

td {
    vertical-align: top;
}

td>div:last-child {
    padding-bottom: .5vw;
}

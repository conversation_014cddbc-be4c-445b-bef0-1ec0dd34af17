/*  (c) 2014 <PERSON>  */
/*  ColorPicker widget for jQuery UI  */
/* https://github.com/evoluteur/colorpicker */

.evo-pop { filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#E5FFFFFF', endColorstr='#E5FFFFFF');background:rgba(255,255,255,0.9);
	z-index:10000;
	width:204px;
	padding:3px 3px 0; border: solid 1px #AAA;
}
.evo-pop-ie { background-color: #FFF;
	z-index:10000;
	width:212px;
	padding:3px;
}

.evo-palette, .evo-palette-ie  {
	border-collapse: separate;
	border-spacing: 4px 0px;
	*border-collapse: expression('separate', cellSpacing = '2px');
}
.evo-palette td  {
	font-size:1px;
	height: auto !important;
	border:solid 1px #c0c0c0;
	padding:7px!important;
	cursor:pointer;
}
.evo-palette tr.top td {
	border-bottom:0;
}
.evo-palette tr.in td {
	border-top:0;
	border-bottom:0;
}
.evo-palette tr.bottom td {
	border-top:0;
} 
.evo-palette th, .evo-palette-ie th  {
	line-height: normal !important;
	height: auto !important;
	border:0 !important;
	padding:5px 3px !important;
	text-align:left;
	font-weight:normal;
	background:transparent !important;
}
.evo-palette div.sep {
	height:3px;
}
.evo-palette-ie td  {
	font-size:1px;
	border:solid 1px #c0c0c0;
	height: auto !important;
	padding: 7px !important;
	cursor:pointer;
} 

.evo-palette2, .evo-palette2-ie {
	margin:auto;
	border-collapse:collapse;
}
.evo-palette2 td, .evo-palette2-ie td{
	font-size:1px;
	cursor:pointer;
}
.evo-palette2 td{ height: auto !important;
	padding:6px 7px !important;
}
.evo-palette2-ie td{ height: auto !important;
	padding:5px !important;
} 
.evo-palcenter{
	padding:5px;
	text-align:center;
}

.evo-colorind,.evo-colorind-ie,.evo-colorind-ff{
	border:solid 1px #c3c3c3;
	width:20px;
	height:20px;
	float:right;
}
.evo-colorind{
	position:relative;
	top:2px;
}
.evo-colorind-ie{
	position:relative; *top:-31px/*IE6,7*/;
}
.evo-colorbox-ie{
	font-size:8px;
	padding:3px 9px !important;
}
.evo-colortxt-ie{
	position:relative;
	top:-6px;
}
.evo-pop:after,
.evo-pop-ie:after,
.evo-colorind:after, 
.evo-colorind-ie:after, 
.evo-colorind-ff:after, 
.evo-color span:after,
.evo-cHist:after{
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
	font-size: 0;
}
.evo-color{
	width:94px;
	padding: 1px 3px 0 4px;
}
.evo-color div{
	border:solid 1px #808080;
	border-right:solid 1px #c0c0c0;
	border-bottom:solid 1px #c0c0c0;
	padding:3px;
	margin-bottom:5px;
	width:10px;
	height:10px;
	float:left;
}
.evo-color span{
	font-size:15px;
	margin: 1px 0 4px 3px;
	float:left;
}
.evo-sep{
	height:10px;
	font-size:0;
}
.evo-more{
	padding:4px 5px 4px;
	font-size: 11px;
	text-align: center;
}
.evo-more a { color: #27A9E3; }
.evo-cHist{
	padding:3px;
}
.evo-cHist div{
	cursor: pointer;
	border:solid 1px #c0c0c0;
	padding:3px;
	margin:5px;
	width:10px;
	height:10px;
	float:left;
}
a.evo-hist{
	margin-left:6px;
}
.evo-pointer{
	cursor:pointer;
}

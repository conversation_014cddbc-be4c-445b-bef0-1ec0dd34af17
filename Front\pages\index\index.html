<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/index.css">
    <link rel="stylesheet" href="../../modules/swiper/basic/css/idangerous.swiper.css">
    <!-- 本页私有js -->
    <script src="../../modules/swiper/basic/js/idangerous.swiper.min.js"></script>
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
</head>

<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="header">
            <div style="margin-left: auto;"></div>
            <img src="../../images/icons/hlk.png" style="object-fit: contain;" class="headLogo">
            <div class="headerItem" style="width: 35%;min-width: 450px;">
                <div class="searchBox">
                    <span class="layui-form headerForm">
                        <select class="select" name="select" id="" style="font-size: 1em;" title="分类">
                            <option value="1">商家</option>
                            <option value="2">品牌</option>
                            <option value="3">型号</option>
                            <option value="4" selected>产品规格</option>
                        </select>
                    </span>

                    <span class="line">  </span>

                    <input class="searchInput" placeholder="请输入产品规格/型号/品牌/商家">
                    <button class="searchButton flex">
                        <div class="iconfont icon-search" style="margin-right: 5px;font-size: 20px;"></div>
                        搜索
                    </button>
                    <!-- @* 推荐关键字 *@ -->
                    <div class="header_keyBox flex">
                        <input class="header_keyCheck" type="radio" id="option1" name="menuOptions" checked>
                        <label class="header_key textOver" for="option1">PM01</label>

                        <input class="header_keyCheck" type="radio" id="option2" name="menuOptions">
                        <label class="header_key textOver" for="option2">LD2451</label>

                        <input class="header_keyCheck" type="radio" id="option3" name="menuOptions">
                        <label class="header_key textOver" for="option3">B0505S-1WR3</label>
                    </div>
                   
                </div>
            </div>
            <div class="headerItem" style="width: 130px;">
                <div class="flex textOver">
                    <input type="checkbox" name="" id="goods1">
                    <label for="goods1" style="margin: -4px 5px;">库存量</label>
                </div>
                <div class="flex textOver" style="margin-left: auto;">
                    <input type="checkbox" name="" id="goods2">
                    <label for="goods2" style="margin: -4px 5px;">PHUJ</label>
                </div>
            </div>
            <div class="headerItem acountBox" style="color: var(--text-color);font-size: 1.1em;position: relative;margin-left: auto;">
                <!-- 登录后 -->
                <div class="isLogin" data-login="true">
                    <i class="iconfont icon-weidenglu icon"style="font-size: 1.4em !important;margin-right: 12px;"></i>
                    <span style="cursor: default;">账号信息</span>
                </div>
                <!-- 未登录 -->
                <div class="isLogin flex" data-login="false" style="font-size: 14px;">
                    <i class="iconfont icon-weidenglu"style="font-size: 1.8em !important;margin: -3px 3px 0 0;"></i>
                    <div class="hoverText textOver" onclick="toRouter(this)" data-link="../login/login.html">登录</div>/
                    <div class="hoverText textOver" onclick="toRouter(this)" data-link="../login/register.html">注册</div>
                </div>
                <div class="acountInfo">
                    <div class="loginOut isLogin" data-login="true">退出</div>
                    <div class="acountHead flex isLogin" data-login="true" data-login_flex="true">
                        <div class="">
                            <img src="../../images/icons/morentouxiang.png">
                        </div>
                        <div class="">
                            <div class="flex"> <div class="textSelect">138****5680</div> <div style="margin-left: auto;">您好！</div></div>
                            <div style="margin-top: 0.2vw;white-space: nowrap;font-size: .52vw;">Hi~欢迎来到海凌科商城！</div>
                        </div>
                    </div>
                    <div class="loginOutBox isLogin" data-login="false" data-login_flex="true">
                       <div style=" background-color: var(--blue-deep);
                       border: 1px solid var(--line);
                       color: white;">登录</div>
                       <div>注册</div>
                    </div>
                    <div class="acountArea">
                        <div onclick="toRouter(this)"><div class="iconfont icon-weidenglu icon"></div> <div>账号信息</div></div>
                        <div onclick="toRouter(this)"><div class="iconfont icon-a-description2x icon"></div> <div>订单</div></div>
                        <div onclick="toRouter(this)"><div class="iconfont icon-wuliu icon"></div> <div>退货和退款</div></div>
                        <div onclick="toRouter(this)"><div class="iconfont icon-a-cartline icon"></div> <div>购物车</div></div>
                        <div onclick="toRouter(this)"><div class="iconfont icon-heart icon"></div> <div>心愿清单</div></div>
                    </div>
                    <div class="acountArea">
                        <div onclick="toRouter(this)"><div class="iconfont icon-xiaoxitongzhi icon"></div> <div>信息中心</div></div>
                        <div onclick="toRouter(this)"><div class="iconfont icon-edit icon"></div> <div>评价</div></div>
                        <div onclick="toRouter(this)"><div class="iconfont icon-wuliuxinxi icon"></div> <div>发票</div></div>
                    </div>
                    <div class="acountArea" style="border: none;padding-bottom: 0px;">
                        <div onclick="toRouter(this)"><div class="iconfont icon-shezhi2 icon"></div> <div>账户设置</div></div>
                        <div onclick="toRouter(this)"><div class="iconfont icon-dianhua icon"></div> <div>联系方式</div></div>
                        <div onclick="toRouter(this)"><div class="iconfont icon-creditcard icon"></div> <div>支付方式</div></div>
                    </div>
                </div>
               
            </div>
            <div class="headerItem" style="color: var(--text-color);font-size: 1.1em;width: 80px;">
                <i class="iconfont icon-cart icon" style="font-size: 1.4em;margin-right: .2em;"></i>
                <span class="hoverText textOver">购物车</span>
            </div>
        </div>
        <!-- 头部分类 -->
        <div class="header2">
            <div class="flex productCategory" data-select="false" onclick="isSelect_productCategory(this)"
                style="margin-left: 10%;user-select: none;cursor: pointer;">
                <label for="select" class="iconfont icon-fenlei2" style="font-size: 26px;">
                </label> 产品分类 <i class="iconfont icon-xiangxia"></i>
                <div class="productCategoryBox">
                    <!-- 一级页面 -->
                    <div class="productCategory_onePage">
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> ACDC电源模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> DCDCD电源模组</span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 雷达模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> WIFI模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> BLE模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 人脸识别 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    </div>
                    <div class="productCategory_hoverPages">

                        <div class="twoPage">
                            <div data-select="true" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> ACDC电源模组 </span>
                            </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> DCDCD电源模组</span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 雷达模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> WIFI模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> BLE模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 人脸识别 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 指纹模组 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 机壳电源 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模组 </span>
                            </div>
                        </div>

                        <div class="threePage">
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div> 

                        </div>
                        <div class="threePageMore">更多</div>
                    </div>
                    <!--  -->
                </div>
            </div>
            <span class="line2" style="height: 17px;margin-left: 10px;"></span>
            <div onclick="toRouter(this)" data-link="./index.html">
                <label class="header2_key">首页</label>
            </div>
            <!-- 第一版 -先干到海凌科品牌商去 -->
            <div onclick="toRouter(this)" data-link="../provider/providerDetail.html">
                <!-- <div onclick="toRouter(this)" data-link="../provider/provider.html"> -->
                <label class="header2_key">供应商</label>
            </div>
            <div>
                <label class="header2_key" onclick="toRouter(this)" data-link="../index/bomTool.html">Bom Tool</label>
            </div>
            <div>
                <label class="header2_key">RFQ</label>
            </div>
            <div>
                <label class="header2_key">技术资源</label>
            </div>
            <div>
                <label class="header2_key" onclick="toRouter(this)" data-link="../index/helper.html">帮助中心</label>
            </div>

            <div class="flex" style="margin-left: auto;margin-right: 10%;position: relative;">
                更改语言：<span class="lngText" onclick="showLngBoxFn()">中文</span>
                <div style="margin-left: 2vw;font-size: 13px;">
                    货币：
                    <img src="../../images/icons/zhongguo.png" style="width: 1.5vw;object-fit: contain;">
                    <span class="lngText" onclick="showLngBoxFn()"
                        style="color: dodgerblue;text-decoration: underline;">RMB￥</span>
                </div>
                <!-- 语言切换 & 货币切换盒子 -->
                <div class="showLngBox" id="showLngBox" data-show="false" style="display: none;">
                    <div>更改语言</div>
                    <div class="layui-form">
                        <select class="layui-select">
                            <option value="0">选择语言</option>
                        </select>
                    </div>
                    <div>货币</div>
                    <div style="position: relative;" class="layui-form">
                        <select class="layui-select">
                            <option class="option1">请选择</option>
                        </select>
                    </div>
                    <div style="margin-top: .5vw;">
                        <button class="layui-btn" onclick="confirmLngFn()"
                            style="background-color: var(--blue-deep);border-radius: 30px !important;">确定</button>
                    </div>
                </div>
            </div>
        </div>
    <!-- 头部结束 -->
    <!-- 主体开始 -->
    <div class="mainBox0">
        <div class="mainBox1_container">
            <!-- 轮播图区域 -->
            <div class="mainBox1">
                <div class="mainBox1_left">
                    <!-- 一级页面 -->
                    <div class="onePage">
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> ACDC电源模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> DCDCD电源模组</span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                        </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 雷达模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> WIFI模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> BLE模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模块 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 人脸识别 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 指纹模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 机壳电源 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                        <div data-select="false" onclick="changeSelect(this,true)"> <span style="width: 80%;"
                                class="textOver"> 路由模组 </span> <i class="iconfont icon-jiantou2 rightIcon"></i> </div>
                    </div>
                    <div class="hoverPages">
                        <!-- 二级页面 -->
                        <div class="twoPage">
                            <div data-select="true" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> ACDC电源模组 </span>
                            </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> DCDCD电源模组</span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 雷达模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> WIFI模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> BLE模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模块 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 人脸识别 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 指纹模组 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 机壳电源 </span> </div>
                            <div data-select="false" onclick="changeSelect(this)"> <span style="width: 80%;"
                                    class="textOver"> 路由模组 </span>
                            </div>
                        </div>
                        <!-- 三级页面 -->
                        <div class="threePage">
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device.png">
                                <div class="threePage_contentBox_name"> ACDC电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>
                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                            <div class="threePage_contentBox" onclick="changeSelect(this)">
                                <img src="../../images/icons/device2.png">
                                <div class="threePage_contentBox_name"> DFAS电源模组 <span
                                        style="color: var(--text-color);">(128)</span> </div>
                            </div>

                        </div>
                        <div class="threePageMore">更多</div>
                    </div>
                    <!-- 三级页面结束 -->
                </div>
                <div class="mainBox1_center">
                    <div>
                        <div class="mainBox1_center_left">
                        
                            <div class="swiper-container">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide"><img src="../../images/icons/denglu-bg.png" alt=""></div>
                                    <div class="swiper-slide"><img src="../../images/icons/denglu-bg.png" alt=""></div>
                                    <div class="swiper-slide"><img src="../../images/icons/denglu-bg.png" alt=""></div>
                                </div>
                                <!-- 如果需要分页器 -->
                                <div class="pagination"></div>

                                <!-- 如果需要导航按钮 -->
                                <div class="swiper-button-prev"></div>
                                <div class="swiper-button-next"></div>

                                <!-- 如果需要滚动条 -->
                                <div class="swiper-scrollbar"></div>
                            </div>
                            <!-- <img src="../../images/icons/denglu-bg.png" style="width: 98%;height: 100%;object-fit: cover;"> -->
                        </div>

                        <div class="mainBox1_center_right">
                            <div class="isLogin" data-login="false">
                                <div style="padding-top: .7vw;">Hi~ 欢迎来到海凌科商城！</div>
                                <div style="padding-top: 1.4vw" class="flex">
                                    <button class="loginBtn button" data-link="../login/login.html"
                                        onclick="toRouter(this)">登录</button>
                                    <button class="registerBtn button" data-link="../login/register.html"
                                        onclick="toRouter(this)">注册</button>
                                </div>
                            </div>
                           
                            <div class="isLogin userInfoBox2" data-login="true">
                                <div class="acountHead flex isLogin" data-login="true" data-login_flex="true">
                                    <div class="pointer" style="width: 23%;margin-right: 5%;" onclick="toRouter(this)" data-link="../userInfo/accountInfo.html">
                                        <img src="../../images/icons/morentouxiang.png">
                                    </div>
                                    <div class="textOver" style="font-size: 13px;">
                                        <div class="flex pointer"> <div class="textSelect" onclick="toRouter(this)" data-link="../userInfo/accountInfo.html">138****5680</div> <div style="margin-left: auto;">您好！</div></div>
                                        <div class="textOver" style="margin-top: 0.2vw;white-space: nowrap;">Hi~欢迎来到海凌科商城！</div>
                                    </div>
                                </div>
                                <div class="flex">
                                    <div>
                                        <div class="iconfont icon-creditcard"></div>
                                        <div>待支付</div>
                                    </div>
                                    <div>
                                        <div class="iconfont icon-wuliuxinxi"></div>
                                        <div>未发货</div>
                                    </div>
                                    <div>
                                        <div class="iconfont icon-wuliu"></div>
                                        <div>配送中</div>
                                    </div>
                                    <div>
                                        <div class="iconfont icon-yishouhuo1"></div>
                                        <div>已签收</div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div style="padding-top: .7vw;display: flex;">
                                    <div style="width: 80%;margin-left: 10%;font-weight: bold;text-align: center;">海凌科公告
                                    </div>
                                    <div class="more">更多</div>
                                </div>
                                <div class="noticeBox">
                                    <div class="textOver">1.随便来个广告随便来个广告随便来个广告</div>
                                    <div class="textOver">2.随便来个广告随便来个广告随便来个广告</div>
                                    <div class="textOver">3.随便来个广告随便来个广告随便来个广告</div>
                                    <div class="textOver">4.随便来个广告随便来个广告随便来个广告</div>
                                </div>
                            </div>
                            <div class="flex">
                                <img src="../../images/icons/gonghuoshang.png" onclick="toRouter(this)" data-link="../../sellerPages/login/sellerLogin.html"
                                    style="object-fit: cover;height: 80%;max-width: 90%;border: 2px dotted rgb(189, 188, 188);">
                            </div>
                        </div>
                    </div>
                    <div class="mainBox1_bottom">
                        <div class="flex" style="justify-content: left;"><img
                                style="margin-left: 1.7em;max-height: 66%;margin-right: .5em;"
                                src="../../images/icons/BOM.png" alt="">随便</div>
                        <div class="flex" style="justify-content: left;"><img
                                style="margin-left: 1.7em;max-height: 66%;margin-right: .5em;"
                                src="../../images/icons/RFQ.png" alt="">随便</div>
                        <div class="flex" style="justify-content: left;"><img
                                style="margin-left: 1.7em;max-height: 66%;margin-right: .5em;"
                                src="../../images/icons/youhuiquan.png" alt="">随便</div>
                        <div class="flex" style="justify-content: left;"><img
                                style="margin-left: 1.7em;max-height: 66%;margin-right: .5em;"
                                src="../../images/icons/shengshu.png" alt="">随便</div>
                    </div>
                </div>
            </div>

        </div>
      
        <div class="mainBox2 flex" style="justify-content: space-around;">
            <div>
                <div class="mainBox2_title flex" style="padding: 10px 20px;font-size: 2vw;">
                    新品推荐
                    <img src="../../images/icons/xinpintuijian.png" alt="查看更多"
                        style="margin-left: auto;cursor: pointer;">
                </div>
                <div class="mainBox2_title" style="padding: 0px 20px;">新品限时5折优惠</div>
                <div class="mainBox2_container">
                    <div class="mainBox2_content">
                        <div>
                            <img src="../../images/icons/device.png" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支</div>
                        <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                        </div>
                        <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    </div>
                    <div class="mainBox2_content">
                        <div>
                            <img src="../../images/icons/device.png" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支</div>
                        <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                        </div>
                        <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    </div>
                    <div class="mainBox2_content">
                        <div>
                            <img src="../../images/icons/device.png" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支</div>
                        <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                        </div>
                        <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    </div>
                    <div class="mainBox2_content">
                        <div>
                            <img src="../../images/icons/device.png" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支</div>
                        <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                        </div>
                        <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    </div>
                </div>
            </div>
            <div>
                <div class="mainBox2_title flex" style="padding: 10px 20px;font-size: 2vw;">
                    新品推荐
                    <img src="../../images/icons/zhekoupin.png" alt="查看更多" style="margin-left: auto;cursor: pointer;">
                </div>
                <div class="mainBox2_title" style="padding: 0px 20px;">新品限时5折优惠</div>
                <div class="mainBox2_container">
                    <div class="mainBox2_content">
                        <div>
                            <img src="../../images/icons/device.png" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支</div>
                        <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                        </div>
                        <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    </div>
                    <div class="mainBox2_content">
                        <div>
                            <img src="../../images/icons/device.png" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支</div>
                        <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                        </div>
                        <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    </div>
                    <div class="mainBox2_content">
                        <div>
                            <img src="../../images/icons/device.png" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支</div>
                        <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                        </div>
                        <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    </div>
                    <div class="mainBox2_content">
                        <div>
                            <img src="../../images/icons/device.png" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支</div>
                        <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                        </div>
                        <div class="mainBox2_content_price">¥3.68~¥4.68</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 新品推荐结束 -->
        <!-- 排行榜开始 -->
       
        <div class="rangeBox">
            <div class="title flex">
                排行榜
                <div class="more" style="font-size: .9vw;margin-right: 50px;">更多</div>
            </div>
            <div class="rangeFunc flex" style="justify-content: left;margin-top: 1vw;">
                <div>销量排行榜</div>
                <div class="textSelect">热销排行榜</div>
            </div>
            <div class="rangeContainer">
                <div>
                    <div class="rangeContent">
                        <div>
                            <img src="../../images/icons/device2.png" alt="排行榜商品" style="width: 100%;">
                        </div>
                        <div>
                            <div style="font-size: .9vw;display: flex;" class="textOver"> <span
                                    class="textSelect">人体存在雷达模块</span> (218)</div>
                            <div style="margin-top: .3vw;">HLK-2420</div>
                        </div>
                    </div>
                    <div class="rangeContent">
                        <div>
                            <img src="../../images/icons/device2.png" alt="排行榜商品" style="width: 100%;">
                        </div>
                        <div>
                            <div style="font-size: .9vw;display: flex;" class="textOver"> <span
                                    class="textSelect">人体存在雷达模块</span> (218)</div>
                            <div style="margin-top: .3vw;">HLK-2420</div>
                        </div>
                    </div>
                    <div class="rangeContent">
                        <div>
                            <img src="../../images/icons/device2.png" alt="排行榜商品" style="width: 100%;">
                        </div>
                        <div>
                            <div style="font-size: .9vw;display: flex;" class="textOver"> <span
                                    class="textSelect">人体存在雷达模块</span> (218)</div>
                            <div style="margin-top: .3vw;">HLK-2420</div>
                        </div>
                    </div>
                </div>
                <div></div>
                <div></div>
                <div></div>
            </div>
        </div>
        <!-- 排行榜结束 -->

          
        <!-- 猜你喜欢 mainBox2_container-->
        <div class="guessLikeBox">
            <div class="title flex">
                猜你喜欢
                <div class="more" style="font-size: .9vw;margin-right: 50px;">更多</div>
            </div>
            <div class="guessContainer">
                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                    </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>
                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                    </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>
                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                    </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>
                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                    </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>
                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star"
                            style="color: var(--warning);font-size: 1vw;font-size: 1vw;"></i>4.9 </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>
                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                    </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>

                <!-- 第二层 -->

                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                    </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>
                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                    </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>
                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                    </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>
                <div class="guessContent">
                    <div>
                        <img src="../../images/icons/device3.png" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">海凌科人体存在传感器LD2410B毫米波 雷达感应模块支持光敏带蓝牙</div>
                    <div>34人购买 <i class="iconfont icon-star" style="color: var(--warning);font-size: 1vw;"></i>4.9
                    </div>
                    <div class="guessContent_price">¥3.68~¥4.68</div>
                </div>

            </div>

        </div>

        <!-- 猜你喜欢结束 -->

        <!-- 供货商品牌开始 -->
        <div class="providerBox">
            <div class="title flex">
                供货商品牌
                <div class="more" style="font-size: .9vw;margin-right: 50px;">更多</div>
            </div>
            <div class="providerContainer">
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
                <div class="providerContent">
                    <div>
                        <img src="../../images/icons/weixin-landi.png" style="width: 100%;object-fit: contain;">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 主体结束 -->

    <!-- 底部开始 -->
    <div class="footer">
        <div class="footer1">
            <div class="footer1Content" style="margin-left: auto;">
                <div style="font-size: 3vw;">15+</div>
                <div>多年IOT互联网</div>
                <div>模组经验</div>
            </div>
            <div class="footer1Content">
                <div style="font-size: 3vw;">4000<span style="font-size: 2vw;">+m2</span> </div>
                <div>工厂面积</div>
            </div>
            <div class="footer1Content">
                <div style="font-size: 3vw;">42</div>
                <div>研发工程师</div>
            </div>
            <div class="footer1Content">
                <div style="font-size: 3vw;">200+</div>
                <div>员工</div>
            </div>
            <div class="footer1Content" style="margin-right: auto;">
                <div style="font-size: 3vw;">15+</div>
                <div>全球企业用户</div>
            </div>
        </div>
        <div class="footer2">
            <div class="footer1Content">
                <div>深圳市海凌科电子有限公司</div>
                <div>电话 : 0755-23152658</div>
                <div>邮箱 : <EMAIL></div>
                <div>地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>公司</div>
                <div>关于我们</div>
                <div>新闻中心</div>
                <div>品质保证</div>
                <div>提交工单</div>
                <div>企业社会责任</div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>资源</div>
                <div>新品速递</div>
                <div>代理招商</div>
                <div>应用场景</div>
                <div>服务和工具</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>支持</div>
                <div>联系我们</div>
                <div>帮助</div>
                <div>反馈</div>
                <div>Cookie政策</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <!--  -->
            <div class="footer1Content">
                <div style="color: white;">联系我们</div>
                <div class="footerLogo">
                    <img src="../../images/icons/weixin-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/youjian-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/wangzhi-landi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/skype-baidi.png" alt="" style="height: 100%;">
                </div>
                <div><img src="../../images/icons/gzh.png" alt="" style="width: 5vw;max-width: 75px;"></div>
                <div>关注微信订阅号</div>
                <div></div>
                <div></div>
            </div>
        </div>
        <div class="footer3">
            联系电话 : 0755-23152658 版权所有 : 深圳市海凌科电子有限公司 备案号 : 粤ICP备12055399号-1 技术支持 : 极思灵创
        </div>
    </div>
    <!-- 底部结束 -->

    <!-- 侧边功能栏 -->
    <div class="aside">
        <div class="asideContainer">
            <div class="asideContent">
                <img src="../../images/icons/gouwuche.png" alt="">
                <div>购物车</div>
            </div>
            <div class="asideContent">
                <img src="../../images/icons/bangzhuzhongxin.png" alt="">
                <div>帮助中心</div>
            </div>
            <div class="asideContent">
                <img src="../../images/icons/xinxizhongxin.png" alt="">
                <div>账号信息</div>
            </div>
            <div class="asideContent">
                <img src="../../images/icons/zaixiankefu.png" alt="">
                <div>在线客服</div>
            </div>
            <div class="asideContent">
                <img src="../../images/icons/lishijilu.png" alt="">
                <div>浏览记录</div>
            </div>
            <div class="asideContent" onclick="scrollToTop()">
                <img src="../../images/icons/dingbu.png" alt="">
                <div>返回顶部</div>
            </div>
        </div>
        <!-- pageBox结束 -->
    </div>
    <script>
        // 轮播图css
        var mySwiper = new Swiper('.swiper-container', {
            autoplay: 5000,//可选选项，自动滑动
            loop: true,//可选选项，开启循环
            // 如果需要分页器
            pagination: '.pagination',
            paginationClickable: true,
            mousewheelControl: true,
        })
        // 监听鼠标hover事件
        var productCategoryBox_dom = $(".productCategoryBox");
        productCategoryBox_dom.mouseover(function (e) {
            if (e.target.getAttribute("data-select")) {
                $(e.target).siblings().attr("data-select", "false");
                e.target.setAttribute("data-select", "true");
            }
        })
        // 监听鼠标hover事件
        var mainBox1_left_dom = $(".mainBox1_left");
        mainBox1_left_dom.mouseover(function (e) {
            if (e.target.getAttribute("data-select")) {
                $(e.target).siblings().attr("data-select", "false");
                e.target.setAttribute("data-select", "true");
            }
        })
        function handleScroll() {
            // 到顶 或者到底 就隐藏
            if (isAtTop() || isAtBottom()) {
                $('.aside').fadeOut(300);
            }else{
                $('.aside').fadeIn();
            }
            // 处理滚动事件的逻辑
        }

        // 节流处理滚动事件
        const throttledScroll = throttle(handleScroll, 200);
        window.addEventListener('scroll', throttledScroll);
        // document.addEventListener('mousewheel', function (e) {
           
        // })
    </script>
</body>

</html>
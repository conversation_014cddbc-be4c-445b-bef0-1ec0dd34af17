<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码</title>
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <script src="../../modules/layui/layui.js"></script>
    <!-- <script src="../../ulits/index.js"></script> -->
</head>
<style>
    .pageBox {
        position: relative;
        /* background-color: #F1F5FA; */
        width: 100%;
        height: 100vh;
        top: 0;
        left: 0;
    }

    .loginBg {
        position: absolute;
        top: 0;
        /* left: -100px; */
        width: 100%;
        height: 100%;
        z-index: 0;
        object-fit: cover;
    }

    .logo {
        position: absolute;
        top: 6%;
        left: 8%;
        z-index: 0;
        object-fit: contain;
    }

    .loginBox {
        position: absolute;
        right: calc(20% - 120px);
        top: 20%;
        width: 400px;
        /* height: 600px; */
        background-color: white;
        border-radius: 5px;
        padding: 50px 30px 70px 30px;
        box-shadow: 4px 4px 16px 0px rgba(0, 0, 0, 0.05), -4px -4px 16px 0px rgba(0, 0, 0, 0.05);
        border: 1px solid #E2E3E9;
        transition: all .5s ease-out;
    }

    .loginItem {
        width: 100%;
        padding: 15px 0px 7px 0px;
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .title {
        position: relative;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #2E2E2E;
        line-height: 33px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }

    .title:before {
        position: absolute;
        content: "";
        left: 25%;
        width: 50%;
        height: 3px;
        background-color: var(--blue-deep);
        bottom: -10px;
    }

    .input {
        width: 70%;
        border-radius: 0px;
    }
    .input:focus{
        border:1px solid #2C79E8 !important;
    }

    .button {
        width: 77.5%;
        padding: 11px;
        background-color: var(--blue-deep);
        color: white;
        border-radius: 3px;
        border: 1px solid var(--blue-deep);
        letter-spacing: 4px;
    }

    .logoBox {
        padding: 0px;
        height: 40px;
    }

    /* 隐藏原生小眼睛 */
    input[type="password"]::-ms-reveal {
        display: none;
    }

    .eyesbox {
        position: absolute;
        width: 12%;
        height: 70%;
        /* background-color: rgba(206, 217, 228, 0.671); */
        right: 10%;
        filter: drop-shadow(0 0 4px #ccc);
    }

    /** 密码框的小眼睛 纯css控制 */
    #eyeInput[type="checkbox"]:checked ~ .close {
        display: block;
    }

    #eyeInput[type="checkbox"]:checked ~ .show {
        display: none;
    }

    #eyeInput[checked] ~ .close {
        display: none;
    }

    #eyeInput[checked] ~ .show {
        display: block;
    }


    /** 密码框的小眼睛 纯css控制 --2*/
    #eyeInput2[type="checkbox"]:checked ~ .close2 {
        display: block;
    }

    #eyeInput2[checked] ~.close2 {
        display: none;
    }

    #eyeInput2[type="checkbox"]:checked~.show2 {
        display: none;
    }

    #eyeInput2[checked]~.show2 {
        display: block;
    }

    .codeBtn {
        width: fit-content;
        font-size: 12px;
        letter-spacing: 1px;
        background-color: #c1dde8;
        color: rgb(0, 127, 253);
        border: 1px solid rgb(98, 192, 230);


    }
    .codeBtn:hover{
        color: white !important;
    }
    .layui-unselect{
        min-width: calc(80% - 8px);
        background-color: var(--bd);
    }
    .eyesbox>.layui-form-checkbox{
        display: none;
    }
    .load{
        width: 15px;
        height: 15px;
        margin-right: 5px;
        color: white;
    }
</style>

<body>
    <div class="pageBox">
        <img src="../../images/icons/denglu-bg.png" class="loginBg">
        <img src="../../images/icons/hlk.png" class="logo">
        <form class="loginBox layui-form">
            <div class="loginItem" style="padding: 0px 0px 30px 0px;">
                <div class="title">重置密码</div>
            </div>
            <!-- 邮箱 -->
            <div class="loginItem">
                <input class="input" name="email" placeholder="请输入邮箱" lay-verify="required|email" lay-reqtext="请输入正确的邮箱">
            </div>
            <!-- 验证码 -->
            <div class="loginItem">
              <div class="flex" style="width: calc(70% + 30px);">
                    <input type="tel" name="code" lay-verify="required|number" placeholder="请输入验证码" class="input"
                    lay-reqtext="请输入邮箱验证码" style="width: 55%;margin-right: auto;">
                   
                    <button type="button" class="button codeBtn" onclick="getCode()">
                        获取验证码
                    </button>
              </div>
            </div>
            <!-- 密码 -->
            <div class="loginItem" style="position: relative;">
                <input class="input" id="pwd" type="password" name="password" placeholder="请输入密码" lay-verify="required|password">
                <div class="eyesbox flex" id="eyesbox">
                    <input type="checkbox" id="eyeInput" class="changeInputType" checked data-num="1" onchange="changeInputType(this)">
                    <label for="eyeInput" class="close">
                        <i class="iconfont icon-denglu-mimabukejian"
                        style="font-size: 23px;color: var(--text-color);"></i>
                    </label>

                    <label for="eyeInput" class="show">
                        <i class="iconfont icon-denglu-mimakejian"
                        style="font-size: 23px;color: var(--text-color);"></i>
                    </label>
                </div>
            </div>
            <!-- 再次确认密码 -->
            <div class="loginItem" style="position: relative;">
                <input class="input" id="pwd2" type="password" name="password2" placeholder="请再次确认密码" lay-verify="required|enPassword">
                <div class="eyesbox flex" id="eyesbox2">
                    <input type="checkbox" id="eyeInput2" class="changeInputType" checked data-num="2" onclick="changeInputType(this)">
                    <label for="eyeInput2" class="close2">
                        <i class="iconfont icon-denglu-mimabukejian"
                        style="font-size: 23px;color: var(--text-color);"></i>
                    </label>
                    <label for="eyeInput2" class="show2">
                        <i class="iconfont icon-denglu-mimakejian"
                        style="font-size: 23px;color: var(--text-color);"></i>
                    </label>
                </div>
            </div>
            <!-- 提交 -->
            <div class="loginItem" style="padding-top: 30px;">
                <button class="button flex" lay-submit lay-filter="demo1">
                    <div class="load" style="display: none;">
                        <i class="iconfont icon-jiazai" style="font-size: 15px;"></i>
                    </div>
                    <div>确认</div>
                </button>
            </div>
        </form>
    </div>
    <script>
        // import { validateEmail } from '../../ulits/index.js';
        // const { validateEmail } = require('../../ulits/index.js');
        var $ = layui.$;
        var form = layui.form;
        // dom获取
        // 改变密码演示与否
        var eyeInput = $('#eyeInput');
        var loginBox = $('.loginBox')[0];
        var eyesbox2 = $('#eyesbox2');
        var pwd2 = $('#pwd2');
        var codebtn = $('.codeBtn')[0];
        var load = $('.load')[0];
         // 自定义验证规则
        form.verify({
            password: function(value) {
                if (value.length < 6) {
                    return '密码长度不能小于6位';
                }
                return
            },
            enPassword: function(value) {
                if (value.length < 6) {
                    return '密码长度不能小于6位';
                }
                if ($(`input[name="password"]`).val() !== value) {
                    return '两次密码不一致';
                }
                return
            }
        });
        // 提交事件
        form.on('submit(demo1)', function(data){
            showLoad(true)
            const formData = data.field
            console.log(formData);
            return false; // 阻止默认 form 跳转
        });
        
        /** 获取验证码 */
        const getCode = async () => {
            await showText('发送中..',codebtn)
            .then(async () => setTimeout( async () => await showText('已发送',codebtn),1000) )
            .then(async () => await timer(60) )
            .then( async res =>{
                if (res === '重新发送') {
                    await showText(res,codebtn)
                }else{
                    await showText(res,codebtn)
                }
            })
        }
        /** 切换 密码显示与否 */
        const changeInputType = (e) => {
            var show = e.checked
            var domId = e.getAttribute("data-num") == '1'?'#pwd':'#pwd2';
            $(domId).attr("type", !show ? "text" : "password");
        }
        /** 显示文本 */
        const showText = async (text,dom) => {
            return await new Promise((resolve) => {
                return resolve(dom.innerText = text)
            });
        }
         /** 加载 */
         const showLoad = async (show = false) => {
            console.log(show,load);
            return await new Promise( async (resolve) => {
                if (show) {
                    return resolve(load.style.display = 'block')
                }
                return resolve(load.style.display = 'none')
            });
        }
        /** 倒计时 */
        async function timer(count = 60) {
            return await new Promise((r,j) => {
                let intervalId = setInterval(async() => {
                    if (count > 0) {
                        count--;
                       await showText(count,codebtn)
                    }else{
                        clearInterval(intervalId);
                        r('执行结束')
                    }
                }, 1000);
            });
        }
        const toLogin = () =>{
            window.localStorage.setItem('b',true)
            window.location.href = "./login.html";
        }
        const isMove = localStorage.getItem('a');
        if ( isMove !=undefined && isMove != null) {
            document.addEventListener('DOMContentLoaded',()=> 
                requestAnimationFrame(() => loginBox.style.right = '10%')
            );
        }else{
            loginBox.style.right = '10%';
        }
        window.localStorage.removeItem('a')
    </script>
</body>

</html>
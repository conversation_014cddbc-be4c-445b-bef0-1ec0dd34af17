/* 模块精确搜索页面 */
.main {
    margin-left: 15%;
    width: 70%;
}

.title {
    padding: .8vw 0px;
    font-size: 20px;
}

.filterBox {
    border: 1px solid var(--line);
}

.inputBox>input {
    width: 20%;
}

.checkBox_box {
    padding: 1vw 0px;
    width: fit-content;
}
.checkBox_box label{
    padding: 5px 0;
}

.checkBox_box>div:nth-child(n+2) {
    margin-left: 1vw;
}

/* 精确搜索盒子 */
.precisionBox {
    padding: .5vw 0px 0px 0px;
    background-color: #f1f1f1;
}

.container {
    padding: 1vw 1vw;
    display: flex;
}

.contentBox {
    margin-right: 2vw;
}

.filterName {
    font-size: 16px;
    padding: 0px 1vw 1vw 0.2vw;
    font-weight: 600;
}

._content {
    min-width: 170px;
    background-color: white;
    height: 300px;
}

.paramterBox {
    height: 240px;
    overflow: auto;
}

.paramterBox>div {
    text-indent: .7vw;
    padding: .5vw 0px;
    cursor: default;
}

.paramterBox>div:hover {
    background-color: #f7f7f7;
}

.searchInputBox {
    padding: .5vw;
    width: 90%;
    margin-left: auto;
    position: relative;
    margin-right: 10%;
    background-color: rgb(247, 247, 247);
    font-weight: 500; 
    display: flex;
    place-items: center;
}

.search_input{
    text-indent: 20px;
    width: 100%;
    padding: 0px 10px;
    font-size: 13px;
}

.inputIcon{
    position: absolute;
    margin-top: -2px;
    font-size: 1.2em !important;
    padding-left: 5px;
    color: var(--text-color2);
}


/* 商品详情 */
.goodsInfo {
    min-width: 15vw;
    display: flex;
}

.goodsInfo>div:nth-child(2)>div {
    color: var(--text-color2);
}

.name {
    color: var(--text-color);
}

td {
    vertical-align: top;
}

td>div:first-child {
    padding-top: .5vw;
}

td>div:last-child {
    padding-bottom: .5vw;
}

#pagingBox {
    padding: 2vw;
    text-align: right;
}

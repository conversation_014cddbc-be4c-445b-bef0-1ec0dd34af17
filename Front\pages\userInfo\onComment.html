<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <link rel="stylesheet" href="../../modules/swiper/basic/css/idangerous.swiper.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/swiper/basic/js/idangerous.swiper.min.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
</head>
<style>
    .pageBox {
        width: 100%;
        /* height: 100vh !important; */
        background-color: #F5F5F5;
    }

    .logo {
        width: 100%;
        padding: 1vw 0px;
        background-color: white;
    }

    .main {
        margin-left: 20%;
        padding: 1vw 0px 2vw 0px;
        width: 60%;
        /* height: 70vh; */
    }
</style>

<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="logo">
            <img src="../../images/icons/hlk.png" style="object-fit: contain;width: 137px;margin-left: 20vw;">
        </div>
        <!-- 头部结束 -->
        <div class="main">
            <!-- 面包屑 -->
            <div class="breadBox" style="border: none;">
                <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
                <div>></div>
                <div onclick="toRouter(this)" data-link="../userInfo/accountInfo.html">账号中心</div>
                <div>></div>
                <div class="textSelect" onclick="toRouter(this)" data-link="../userInfo/comments.html">评价</div>
            </div>
            <div class="contaniner">
                <style>
                    .contaniner {
                        padding: 0vw 0px;
                        width: 100%;
                        height: 100%;
                        /* border: 1px solid ; */
                    }

                    .title {
                        font-size: 1.2vw;
                        text-align: center;
                        font-weight: 550;
                    }

                    .orderIdBox {
                        margin: .5vw 0;

                    }

                    .tips {
                        text-align: center;
                        background-color: white;
                        padding-top: 1vw;
                        font-size: .9vw;
                        color: var(--red);
                    }

                    .content {
                        margin-top: 0vw;
                        padding: 0px 1vw 1vw 1vw;
                        background-color: white;
                        display: flex;
                    }

                    aside {
                        width: 30%;
                        height: 100%;
                        margin: 2% 2% 2% 5%;
                        color: var(--text-color);
                    }

                    .goodsImg {
                        margin-left: 20%;
                        width: 60%;
                        height: 30%;
                        display: flex;
                    }

                    .goodsImg>img {
                        width: 90%;
                        height: 80%;
                        object-fit: contain;
                    }

                    .goodsName {
                        font-size: .8vw;
                        /* letter-spacing: 1px; */
                        text-align: center;
                        margin: 1vw 0px;
                    }

                    .right {
                        width: 60%;
                        height: auto;
                        min-height: 50vh;
                        margin-right: auto;
                        padding: 1vw 0px;
                    }

                    .rate {
                        padding: 0px 1vw 1vw 1vw;
                        display: flex;
                        justify-content: space-between;
                        place-items: center;
                        flex-wrap: wrap;
                    }

                    .rate>div {
                        width: 45%;
                        display: flex;
                        place-items: center;
                    }

                    .rate>div>div:nth-child(2) {
                        margin-left: auto;
                    }

                    .rate>div>div {
                        white-space: nowrap;
                        font-size: .8vw;
                    }

                    .comment>div:nth-child(1) {
                        padding-bottom: 1vw;
                        font-size: .9vw;
                        font-weight: 550;
                    }

                    .uploadImagesBox {
                        padding: 1vw 0px;
                        justify-content: left;
                        /* border: 2px solid red; */

                    }

                    .uploadImages {
                        /* width: fit-content !important; */
                        width: 100%;
                        min-height: 80px;
                        /* max-height: 200px; */
                        padding: 0px !important;
                        margin: 0px !important;
                        display: flex;
                        flex-wrap: wrap;
                        /* border: 2px solid red; */
                        /* justify-content: left; */
                    }

                    .uploadImage {
                        /* width: fit-content !important; */
                        padding: 2px;
                        margin: .5vw .5vw .5vw 0px;
                        /* flex: 1; */
                        max-width: 30%;
                        position: relative;
                        border: 1px solid var(--line);
                    }

                    .uploadImage>img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        user-select: none;
                        cursor: pointer;
                    }

                    .delImage {
                        position: absolute;
                        content: '×';
                        border-radius: 50%;
                        font-size: 1.5vw;
                        text-align: center;
                        top: 0px;
                        right: .5vw;
                        color: gray;
                        cursor: pointer;
                        transition: all .3s;
                        z-index: 10;
                        /* background-color: rgba(56, 56, 56, 0.247); */
                    }

                    .delImage:hover {
                        color: red;
                    }

                    .uploadImage:hover::before {
                        color: var(--blue-deep);
                        opacity: 1;
                    }

                    .uploadImage::before {
                        position: absolute;
                        content: ' 查看原图 ';
                        font-size: .8vw;
                        text-align: center;
                        top: 0px;
                        left: 0px;
                        width: 100%;
                        height: 100%;
                        color: gray;
                        cursor: pointer;
                        transition: all .3s;
                        display: flex;
                        place-items: center;
                        justify-content: center;
                        background-color: rgba(56, 56, 56, 0.247);
                        opacity: 0;
                        text-shadow: 0 0 1px #ccc;
                        text-decoration: underline;
                        pointer-events: none;
                    }

                    .addBox {
                        min-width: 120px;
                        min-height: 100px;
                        border: 1px solid #ccc;
                        place-items: center;
                        border-radius: 5px;
                        cursor: pointer;
                        transition: all .5s;
                    }

                    .uploadText {
                        width: 100px;
                        margin-top: 10px;
                        /* width: 100%; */
                        margin-left: .5vw;
                        font-size: .75vw;
                        white-space: nowrap;
                        color: gray;
                        /* text-indent: 10px; */
                        text-align: center;
                    }

                    .sendBox {
                        margin-top: 1vw;
                        padding: 1vw;
                        background-color: white;
                    }
                    .bug {
                        background-color: #F5F5F5;
                    }
                </style>
                <div class="title">评价订单</div>
                <div class="flex orderIdBox">
                    <div>订单编号: SO258103645920</div>
                    <div style="margin-left: 1vw;">2024-06-10 16:48:60</div>
                </div>
                <div class="tips"><i class="iconfont icon-error-circle" style="font-size: 1vw;"></i>
                    请至少填写一件商品的评价</div>
                <div class="content">
                    <aside class="">
                        <div class="goodsImg">
                            <img src="../../images/icons/device3.png" alt="">
                        </div>
                        <div class="goodsName">
                            220V转5V3.3V9V12V15V24V电源模块 HLK-PM01 ACDC隔离电源稳压输出
                        </div>
                        <div style="text-align: center;">
                            <span style="font-size: 1vw;">¥5.99</span> <span class="gray">¥5.99</span> <span
                                class="red">-63%</span>
                        </div>
                    </aside>
                    <div></div>
                    <div class="right">
                        <div class="rate">
                            <div>
                                <div>商品评分</div>
                                <div id="rate1"></div>
                            </div>
                            <div>
                                <div>店家服务态度</div>
                                <div id="rate2"></div>
                            </div>
                            <div>
                                <div>快递配送速度</div>
                                <div id="rate3"></div>
                            </div>
                            <div>
                                <div>快递员服务</div>
                                <div id="rate4"></div>
                            </div>
                            <script>
                                layui.use(function () {
                                    var rate = layui.rate;
                                    // 渲染
                                    for (let i = 1; i <= 4; i++) {
                                        rate.render({
                                            elem: `#rate${i}`,
                                            value: 3.5,
                                            half: true,
                                            text: true,
                                            setText: function (value) { // 自定义文本的初始回调
                                                var arrs = {
                                                    '1': '非常不满意',
                                                    '2': '不满意',
                                                    '3': '一般',
                                                    '4': '满意',
                                                    '5': '非常满意',
                                                };
                                                this.span.text(arrs[value] || (value + "星"));
                                            }
                                        });

                                    }
                                });
                            </script>
                        </div>

                        <div class="comment">
                            <div>评价晒单:</div>
                            <div class="textarea" data-currentCount="0" data-maxLength="/500"
                                style="position: relative;">
                                <textarea name="" maxlength="500" rows="7" oninput="textareaOninput(this)"
                                    placeholder="分享体验心得,给千万想买的人一个参考~" class="layui-textarea"
                                    style="padding: .3vw .3vw 1.5vw .5vw;resize: none;font-size: .8vw;letter-spacing: 1px;"></textarea>
                            </div>
                            <div class="uploadImagesBox flex">
                                <div class="uploadImages" id="uploadImages">
                                    <div class="flex" id="addPicBtn" style="position: relative;">
                                        <div class="addBox column" onclick="addFile()">
                                            <div class="iconfont icon-shangchuan"
                                                style="color: var(--blue-deep);margin-top: 20px;font-size: 1vw;"></div>
                                            <div class="uploadText">
                                                共 <span class="blue" id="imageCount">0</span> 张图片
                                                <div style="text-align: right;letter-spacing: 1px;"></div>
                                                还能上传 <span class="red" style="text-indent: 20px;"
                                                    id="uploadedCount">6</span> 张
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mask flex" id="mask" onclick="hideMask()">

                                </div>
                            </div>

                        </div>
                    </div>
                    <script>
                        /** 添加文件 */
                        function addFile() {
                            const fileInput = document.createElement('input');
                            fileInput.type = 'file';
                            fileInput.onchange = function () {
                                const file = fileInput.files[0];
                                if (file.type == 'image/png' || file.type == 'image/jpeg') {
                                    if (fileInput.files.length > 6 || $('#uploadImages').children().length >= 7) {
                                        layui.layer.msg('最多只能上传6张图片', { icon: 5, duration: 4000 });
                                        return
                                    }
                                    $('#addPicBtn').before
                                    (`<div class="uploadImage">
                                            <img src="${URL.createObjectURL(file)}" onclick="viewThePic(this)">
                                            <div class="delImage" onclick="this.parentNode.remove(),checkRemaining()">×</div>
                                        </div>`)
                                    setTimeout(() => {
                                        checkRemaining()
                                    }, 50);
                                } else {
                                    layui.layer.msg('请上传png或jpg格式的图片');
                                }
                            }
                            fileInput.click()
                            //监听选择文件后删除该input
                            fileInput.remove()
                        }
                        /** 检查还能上传多少张图片 */
                        function checkRemaining(params) {
                            const imageList = $('#uploadImages').children()
                            $('#imageCount').text(imageList.length - 1)
                            $('#uploadedCount').text(7 - $('#uploadImages').children().length)
                            if (imageList.length > 1) {
                                console.log($(imageList));
                                const height = $(imageList[imageList.length-2]).height();
                                const width = $(imageList[imageList.length-2]).width();
                                $('.addBox').css('height', height + 'px')
                                $('.addBox').css('width', width + 'px')
                            }
                        }
                        checkRemaining()
                        /* 查看大图 */
                        function viewThePic(dom) {
                            console.log(dom);
                            const mask = $('#mask');
                            mask.append(`<div class="flex" style="height:100%;width:100%;"> <img class="pointer" src="${dom.getAttribute('src')}"> </di>`);
                            mask.fadeIn(300);
                        }
                    </script>
                </div>
            </div>

            <div class="sendBox flex layui-form">
                <button class="button button_blue"
                    style="width: 30%;padding: .7vw;margin-right: 1vw;font-size: .8vw;">发表</button>
                <input type="checkbox">匿名评价
            </div>

        </div>
    </div>
    <div class="bug"></div>
    <!-- 底部开始 -->
    <div class="footer">
        <div class="footer2">
            <div class="footer1Content">
                <div>深圳市海凌科电子有限公司</div>
                <div>电话 : 0755-23152658</div>
                <div>邮箱 : <EMAIL></div>
                <div>地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>公司</div>
                <div>关于我们</div>
                <div>新闻中心</div>
                <div>品质保证</div>
                <div>提交工单</div>
                <div>企业社会责任</div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>资源</div>
                <div>新品速递</div>
                <div>代理招商</div>
                <div>应用场景</div>
                <div>服务和工具</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <div class="footer1Content">
                <div>支持</div>
                <div>联系我们</div>
                <div>帮助</div>
                <div>反馈</div>
                <div>Cookie政策</div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <!--  -->
            <div class="footer1Content">
                <div class="connectOur">联系我们</div>
                <div class="footerLogo">
                    <img src="../../images/icons/weixin-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/youjian-baidi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/wangzhi-landi.png" alt="" style="height: 100%;">
                    <img src="../../images/icons/skype-baidi.png" alt="" style="height: 100%;">
                </div>
                <div><img src="../../images/icons/gzh.png" alt="" style="width: 5vw;max-width: 75px;"></div>
                <div>关注微信订阅号</div>
                <div></div>
                <div></div>
            </div>
        </div>
        <div class="footer3">
            联系电话 : 0755-23152658 版权所有 : 深圳市海凌科电子有限公司 备案号 : 粤ICP备12055399号-1 技术支持 : 极思灵创
        </div>
    </div>
    <!-- 底部结束 -->
    </div>


</body>

</html>
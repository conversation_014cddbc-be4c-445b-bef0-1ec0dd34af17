<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海凌科商城</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <link rel="stylesheet" href="../../modules/swiper/basic/css/idangerous.swiper.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/swiper/basic/js/idangerous.swiper.min.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
</head>
<style>
    .pageBox {
        width: 100%;
        height: 100vh !important;
        background-color: #F5F5F5;
    }

    .logo {
        width: 100%;
        padding: 1vw 0px;
        background-color: white;
    }

    .main {
        margin-left: 20%;
        padding: 1vw 0px 2vw 0px;
        width: 60%;
        /* height: 70vh; */
    }
</style>

<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="logo">
            <img src="../../images/icons/hlk.png" style="object-fit: contain;width: 137px;margin-left: 20vw;">
        </div>
        <!-- 头部结束 -->
        <div class="main">
            <!-- 面包屑 -->
            <div class="breadBox" style="border: none;">
                <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
                <div>></div>
                <div onclick="toRouter(this)" data-link="../userInfo/accountSettings.html">账户中心</div>
                <div>></div>
                <div class="textSelect" onclick="toRouter(this)" data-link="../userInfo/editMail.html">修改密码</div>
            </div>
            <div class="contaniner">
                <style>
                    .contaniner {
                        min-height: 600px;
                        padding: 1vw;
                        /* border: 1px solid ; */
                    }

                    .box {
                        width: 100%;
                        padding: 20px 10px;
                    }

                    .box:nth-child(n+3) {
                        background-color: white;
                        margin: 10px 0px;
                    }

                    .title {
                        font-size: 30px;
                        margin-bottom: 10px;
                        color: black;
                    }

                    .stepLine {
                        width: 30vw;
                        height: 3px;
                        border: 1px solid white;
                        border-radius: 5px;
                        transition: 1s;
                        position: relative;
                        background-color: var(--text-color4);
                    }

                    .box>.item {
                        /* border: 1px solid ; */
                        padding: 10px 0px;
                    }

                    .circle {
                        border-radius: 50%;
                        padding: 10px 15px;
                        margin-bottom: 5px;
                        background-color: var(--text-color4);
                        color: white;
                    }

                    .text {
                        font-size: 16px;
                    }

                    .tx {
                        width: 50px;
                        height: 50px;
                        border-radius: 50%;
                    }

                    .image {
                        margin-right: 5px;
                        border: 1px solid var(--line);
                    }

                    .contentBox>div {
                        margin-bottom: 10px;
                        /* border: 1px solid ; */
                        width: 50%;
                        min-width: 400px;
                    }

                    .input {
                        width: 15vw;
                        height: 35px;
                        max-width: 250px;
                    }

                    .button {
                        height: 40px;
                    }

                    .skyblueBtn {
                        border: 1px solid var(--blue-deep);
                        background: rgba(0, 148, 234, 0.15);
                        color: var(--blue-deep);
                    }

                    .skyblueBtn:hover {
                        color: rgb(49, 183, 236);
                        border: 1px solid rgb(49, 183, 236);
                    }

                    .icon-ai210 {
                        display: none;
                    }

                    .failText {
                        position: absolute;
                        top: 100%;
                        left: 110px;
                        color: var(--red);
                        font-size: 13px;
                        letter-spacing: 1px;
                        display: none;
                    }

                    /* 验证成功的css */
                    .circle[data-step="2"] {
                        padding: 10px;
                        background-color: var(--blue-deep);
                    }

                    .circle:first-child[data-step="2"] {
                        padding: 10px;
                    }

                    .one[data-step="2"] {
                        display: none;
                    }

                    .icon-ai210[data-step="2"] {
                        display: block;
                    }

                    .text[data-step="2"] {
                        color: var(--blue-deep);
                    }

                    .stepLine[data-step="2"] {
                        background-color: var(--blue-deep);
                    }

                    #contentBox1[data-step="2"] {
                        display: none;
                    }

                    #contentBox2[data-step="2"] {
                        display: none;
                    }
                    #contentBox2[data-step="1"] {
                        display: none;
                    }
                    #contentBox1[data-step="1"] {
                        display: block;
                    }
                    #contentBox3{
                        display: none;
                    }
                    #contentBox3[data-step="2"]{
                        display: block;
                    }
                    
                    /* 切换邮箱/手机号效果 */
                    .titleBox1 {
                        font-size: 16px;
                        color: var(--text-color2);
                        cursor: pointer;
                        display: flex;
                        margin-bottom: 20px !important;
                    }

                    .titleBox1>div {
                        width: 50%;
                        padding: 10px 0px;
                        text-align: center;
                        color: var(--text-color2);
                        position: relative;
                        border: 1px solid var(--text-color4);
                    }

                    .titleBox1>div[data-select="true"] {
                        border: 1px solid var(--blue-deep);
                        color: var(--blue-deep);
                    }

                    .titleBox1>div::before {
                        position: absolute;
                        content: '✔';
                        right: 0%;
                        bottom: 0%;
                        width: 40px;
                        padding: 0vw;
                        background-color: var(--blue-deep);
                        color: white;
                        height: 30px;
                        line-height: 42px;
                        text-indent: 14px;
                        display: none;
                    }

                    .titleBox1>div::after {
                        position: absolute;
                        content: '';
                        right: 0px;
                        bottom: 0%;
                        border-left: 0px solid transparent;
                        border-right: 40px solid transparent;
                        border-top: 30px solid white;
                        display: none;
                    }

                    .titleBox1>div[data-select="true"]::before {
                        display: block;
                    }

                    .titleBox1>div[data-select="true"]::after {
                        display: block;
                    }

                    .titleBox1>div[data-select="true"] #contentBox1 {
                        background-color: red;
                    }

                </style>
                <div class="box" style="padding-top: 0px;">
                    <div class="title textC">修改密码</div>
                    <div class="title2 textC">修改密码后，原密码将失效，请知悉</div>
                </div>

                <div class="box flex" style="margin-bottom: 30px;">
                    <div class="item column">
                        <div class="circle bgSelect" data-step="2">
                            <span class="one" data-step="2">1</span>
                            <i class="iconfont icon-ai210" data-step="2" style="font-size: 20px;"></i>
                        </div>
                        <div class="iconfont icon-xuanzhong" data-step="2"></div>
                        <div class="text textSelect" data-step="2">验证身份</div>
                    </div>
                    <div class="item">
                        <div class="stepLine" data-step="2"></div>
                    </div>
                    <div class="item column">
                        <div class="circle" data-step="2" style="padding: 12px 16px;">
                            2
                        </div>
                        <div class="text" data-step="2">设置新密码</div>
                    </div>
                </div>


                <!-- 手机号验证 -->
                <div class="box contentBox column" id="contentBox1" style="padding: 40px 0px;" data-step="2">
                    <div class="titleBox1">
                        <div data-select="true" onclick="titleClick(this,1)">手机号验证</div>
                        <div data-select="false" onclick="titleClick(this,2)">邮箱验证</div>
                    </div>
                    <div class="flex" style="margin-left: 200px;">
                        <div>
                            <img class="tx image" src="../../images/icons/morentouxiang.png">
                        </div>
                        <div style="margin: 0 auto 0 10px;" class="text">客户ID:1594586A</div>
                    </div>
                    <div class="flex" style="margin-left: 200px;">
                        验证码发送至&nbsp;<span class="textSelect" id="number">15361580137</span>&nbsp;
                        <div style="margin-right: auto;">手机号</div>
                    </div>
                    <div class="flex" style="margin: 20px 0px 20px 200px;">
                        <input type="text" class="input" placeholder="请输入验证码">
                        <button class="button button_blue skyblueBtn" id="getCodeBtn" onclick="getCode(this)"
                            data-codeStep="1" style="margin-right: auto;">获取验证码</button>
                    </div>
                    <div class="flex">
                        <button class="button button_blue" style="width: 55%;margin: 20px 0px 20px 0px;"
                            onclick="next(2)">下一步</button>
                    </div>
                </div>

                <!-- 邮箱验证 -->
                <div class="box contentBox column" id="contentBox2" style="padding: 40px 0px;" data-step="2">
                    <div class="titleBox1">
                        <div data-select="false" onclick="titleClick(this,1)">手机号验证</div>
                        <div data-select="true" onclick="titleClick(this,2)">邮箱验证</div>
                    </div>
                    <div class="flex" style="margin-left: 200px;">
                        <div>
                            <img class="tx image" src="../../images/icons/morentouxiang.png">
                        </div>
                        <div style="margin: 0 auto 0 10px;" class="text">客户ID:1594586A</div>
                    </div>
                    <div style="margin-left: 200px;">
                        验证码发送至 <span class="textSelect" id="email">892****<EMAIL> </span>
                    </div>
                    <div class="flex" style="margin: 20px 0px 20px 200px;">
                        <input type="text" class="input" placeholder="请输入验证码">
                        <button class="button button_blue skyblueBtn" id="getCodeBtn" onclick="getCode(this)"
                            data-codeStep="1" style="margin-right: auto;">获取验证码</button>
                    </div>
                    <div class="flex">
                        <button class="button button_blue" style="width: 55%;margin: 20px 0px 20px 0px;"
                            onclick="next(2)">下一步</button>
                    </div>
                </div>

                <!-- 设置新密码 -->
                <div class="box contentBox column" id="contentBox3" style="padding: 40px 0px 40px 0px;" data-step="2">
                    <div class="flex" style="margin-left: 200px;">
                        <div>
                            <img class="tx image" src="../../images/icons/morentouxiang.png">
                        </div>
                        <div style="margin: 0 auto 0 10px;" class="text">客户ID:1594586A</div>
                    </div>
                    <div class="" style="margin: 20px 0px 20px 150px;">
                        <div class="flex" style="margin-bottom: 20px;position: relative;">
                            <div style="font-size: 16px;">输入新密码：</div>
                            <div style="margin: 0 auto 0 10px;">
                                <input type="text" class="input" placeholder="请输入6-20位新密码" oninput="checkPwd(this,1)">
                            </div>
                            <div class="failText" id="failText1">请输入6-20位新密码</div>
                        </div>
                        <div class="flex" style="margin-bottom: 20px;position: relative;">
                            <div style="font-size: 16px;">确认新密码：</div>
                            <div style="margin: 0 auto 0 10px;">
                                <input type="text" class="input" placeholder="请再次确认新密码" oninput="checkPwd(this,2)">
                            </div>
                            <div class="failText" id="failText2">您输入的密码与新密码不符合</div>
                        </div>
                    </div>
                    <div class="flex">
                        <button class="button button_blue" style="width: 55%;margin: 20px 0px 20px 0px;"
                            onclick="next()">提交</button>
                    </div>
                </div>

            </div>
        </div>
        <script>
            function next(step = 1) {
                const list = $('[data-step]');
                console.log(list);
                list.siblings().attr('data-step', step);
                $('.stepLine').attr('data-step', step); //奇怪
                if (step === 2) {
                    $('#contentBox1').hide()
                    $('#contentBox2').hide()
                }
            }
            next()
            function titleClick(dom, index) {
                if (index == 1) {
                    $('#contentBox2').hide()
                    $('#contentBox1').show()
                }
                else {
                    $('#contentBox1').hide()
                    $('#contentBox2').show()
                }

            }
            var form = {
                new_pwd:'',
            }
            /** 检查密码 */
            function checkPwd(inputDom,index) {
                const val = $(inputDom).val();
                if (index == 1) { // 6 -20位的密码
                    val.length < 6  || val.length > 20 ?$(`#failText${index}`).css('display', 'block'):$(`#failText${index}`).css('display', 'none');
                    form.new_pwd = val
                    // val.length < 6  ? $(`#failText${index}`).text('请输入6-20位新密码').css('display', 'block'):$(`#failText${index}`).css('display', 'none');
                    // val.length > 20 ? $(`#failText${index}`).text('请输入6-20位新密码').css('display', 'block'):$(`#failText${index}`).css('display', 'none');
                }else{
                    if (!val) {
                        $(`#failText${index}`).css('display', 'none');
                        return
                    }
                    val != form.new_pwd ?$(`#failText${index}`).css('display', 'block'):$(`#failText${index}`).css('display', 'none');
                }
            }
            const email = document.getElementById('email').innerText
            document.getElementById('email').innerText = starNumber(email)
            const number = document.getElementById('number').innerText
            document.getElementById('number').innerText = starNumber(number)

        </script>
       
    </div>


</body>

</html>
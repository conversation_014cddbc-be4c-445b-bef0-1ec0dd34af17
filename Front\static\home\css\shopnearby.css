.ds-fl{float:left}
.ds-fr{float:right}
.clear{clear:both}


.ds-content{width:1200px;margin:0 auto;overflow:hidden}
.ds-header .ds-content{overflow:visible;height:80px}
.ds-content .search{top:10px;position:relative}
.search input{margin-top:4px;width:420px;height:25px;border-radius:4px 0 0 4px;background-color: #fff;
    border: 1px solid #ccc;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    -webkit-transition: border linear .2s,box-shadow linear .2s;
    -moz-transition: border linear .2s,box-shadow linear .2s;
    -o-transition: border linear .2s,box-shadow linear .2s;
    transition: border linear .2s,box-shadow linear .2s;display: inline-block;
    padding: 4px 6px;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 20px;
    color: #555;
    vertical-align: middle;}
.search button{background:#47aa4d;border:none;width:35px;height:35px;font-size:20px;font-weight:700;color:#fff;margin-top:15px;border-radius:0 4px 4px 0;margin-left:-4px}
.search i{display:inline-block;width:20px;height:20px;cursor: pointer}
.ds-content .search .shelper{position:absolute;z-index:2;width:232px;height:auto;border:1px solid #ccc;top:61px;background:#fff;box-shadow:1px 2px 1px rgba(0,0,0,.2);border-top:none;border-radius:0 0 4px 4px;display:none}
.ds-content .search .shelper .search-item{float:left;width:110px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}
.ds-content .search .shelper .search-count{overflow:hidden;color:#aaa;text-align:right;white-space:nowrap;text-overflow:ellipsis}
.ds-content .search .shelper li{overflow:hidden;padding:0 6px;line-height:28px;cursor:pointer;height:28px;font-size:13px}

.ds-header{height:80px;background:#fff}
.ds-header .select-location{color:#47aa4d;float:left;line-height:80px;padding:0 10px;font-size:14px;width:120px}
.ds-header .select-location .current-position{font-size:18px;padding:0 5px}
.ds-header .select-location .choose-city{cursor:pointer}
.ds-header .select-location i{font-size:24px;cursor: pointer;width:25px;height:25px;line-height:25px;background-size:25px;background-repeat:no-repeat;float:left;margin-top:26px;background-position:0 0}
.ds-header .nav{overflow:hidden;margin-bottom:0}
.ds-header .nav li{padding:0 17px;font-size:17px;line-height:80px;float:left;color:#333}
.ds-header .nav li.active a{border-bottom:4px solid #47aa4d;color:#47aa4d}
.ds-header .nav>li>a{padding:5px;display:inline}
.ds-header .nav li:last-child{padding-right:0}
.ds-header.fixed{height:80px;}
.ds-header.fixed.navbar-transparent{box-shadow:0 3px 6px rgba(0,0,0,.12);-webkit-box-shadow:0 3px 6px rgba(0,0,0,.12)}




.category .store_list{background-color:#FFF;width:1200px;margin:0 auto;overflow:hidden;padding-bottom:10px}
.category .store_list .store_list_title{width:100%;height:50px;padding-bottom:20px;text-align:center;padding-top:40px}
.category .store_list .panel_sao{position:absolute;z-index:1}
.category .store_list .panel_sao .panel_sao_pa{margin:-10px 0 0 25px}
.category .store_list .store_list_title img{margin-right:20px;width:40px;padding-bottom:.5em}
.category .store_list .store_list_title span{color:#47aa4d;font-size:20px;margin-right:20px}
.category .store_list .category_store{float:left;margin:20px 0 10px 30px;width:358px;height:320px;border:1px solid #eaeaea}
.category .store_list .category_store:hover{box-shadow:5px 5px 15px #eaeaea}
.category .store_list .category_store .announcement{width:100%;position:relative}
.category .store_list .category_store .announcement .favorable{padding:5px 5px 0 10px;height:27px}
.category .store_list .category_store .announcement .favorable span:first-child{font-size:14px;background-color:red;color:#FFF;padding:2px;border-radius:5px;display:inline-block;max-width:260px;overflow:hidden;text-overflow:ellipsis;word-break:keep-all;width:17px;height:17px;text-align:center;line-height:16px}
.category .store_list .category_store .announcement .favorable span{width:310px;word-break:keep-all;font-size:15px;color:#333;padding-left:10px;display:block;float:left;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}
.category .store_list .category_store .announcement .business_time{padding:0 10px}
.category .store_list .category_store .announcement .business_time img{width:22px;height:22px;float:left}
.category .store_list .category_store .announcement .business_time span{letter-spacing:1px;font-style:normal;font-size:12px;color:#333}
.category .store_list .category_store .announcement .advertisement{padding:0 10px}
.category .store_list .category_store .announcement .advertisement .goods-price{line-height:20px;color:#999;word-wrap:break-word;font-size:12px;overflow:hidden;text-overflow:ellipsis;-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box}
.category .store_list .category_store .shop_banner {width:100%;height:120px;background:url(../images/default800.png);background-size:auto 120px;background-repeat: repeat;background-position: center center;}
.category .store_list .category_store .store_info{width:100%;height:70px;margin-top:10px;padding-left:10px;margin-bottom:5px;box-sizing: border-box}
.category .store_list .category_store .store_info .store_info_img{width:70px;height:70px;float:left}
.category .store_list .category_store .store_info .store_msg{float:left;width:278px;height:70px}
.category .store_list .category_store .store_info .store_msg .self{float:left;border:1px solid;background-color:#47aa4d;color:#FFF;width:30px;text-align:center;border-radius:5px;margin-top:2px;margin-left:10px}
.category .store_list .category_store .store_info .store_msg .store_name{max-width:150px;overflow:hidden;text-overflow:ellipsis;-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;font-size:15px;color:#333;margin-left:10px;float:left}
.category .store_list .category_store .store_info .store_msg .store-range{margin-left:10px;margin-top:10px;width:160px}
.category .store_list .category_store .store_info .store_msg .qrcode_small_img{float:right;margin-right:10px}
.category .store_list .category_store .store_info .store_msg .qrcode_small_img img{margin-right:10px;height:70px;width:70px}

.category .store_list .category_store .store_name_wrap{height: 30px}
.category .store_list .category_store .goods_list{margin-top:25px}
.category .store_list .category_store .goods_list img{width:77px;height:77px;margin-left:10px}
.category_list{height:50px;background-color:#f6f6f6;margin-bottom:10px;width:1200px;margin:10px auto}
.ds-category-swiper{padding:0 20px;position:relative}
.category_list .category_name{height:50px;width:100px}
.category_list .category_name .text{padding:0 21px;border-left:1px solid #E5E5E5;margin-left:-1px;display:block;height:21px;margin-top:14px;line-height:20px;text-align:center}
.category_list .category_name .text a{color:#000}
.category_list .category_name .text.green a{color:#47aa4d}
.category_list .category_name .img{position:absolute;width:100%;text-align:center;margin-top:20px}
.category_list .category_name .img img{height:35px;width:35px;min-height:35px}
.category_list_hidden{display:none}
.category_line img{width:100%;float:left}


#category-swiper-web.swiper-container{width:auto;height:50px;background-color:#f6f6f6}
.ds-category-swiper .swiper-button-prev,.swiper-container-rtl .swiper-button-next{left:10px}
.ds-category-swiper .swiper-button-next{right:10px}
#category-swiper-web .swiper-button-next,#category-swiper-web .swiper-button-prev{background-size:15px 44px}
#category-swiper-web .swiper-pagination{top:88px}
#category-swiper-web .swiper-pagination-bullet-active{opacity:1;background:#47aa4d}
.ds-category-swiper .swiper-button-next.swiper-button-white,.ds-category-swiper .swiper-container-rtl .swiper-button-prev.swiper-button-white{background-size:10px 44px;background-color:#f6f6f6;background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23333'%2F%3E%3C%2Fsvg%3E")}
.ds-category-swiper .swiper-button-prev.swiper-button-white,.ds-category-swiper .swiper-container-rtl .swiper-button-next.swiper-button-white{background-size:10px 44px;background-color:#f6f6f6;background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23333'%2F%3E%3C%2Fsvg%3E")}
.swiper-slide p{margin-top:25px;text-align:center;font-size:16px;color:#000}


.ds-no-content{width:100%;height:450px;background-color:#f6f6f6;padding-bottom:30px}
.ds-no-content .content{background-image:url(../images/no_content.png);background-repeat:no-repeat;background-size:100% 100%;width:100%;height:100%}
.ds-no-content .content .content-container{width:400px;margin:0 auto;padding-top:60px;font-size:15px;color:#333;text-align: center;}
.ds-no-content .content .content-container .current-city{line-height:40px;font-size:18px}
.ds-no-content .content .content-container .go_settled{text-align:center;line-height:60px;font-size:15px;border-radius:40px;cursor:pointer;padding:5px 15px;color:#fff;background-color:#47aa4d}
.ds-no-content .content .content-container .choose-city{color:#47aa4d;cursor:pointer;border-bottom:1px solid #47aa4d}


.nearby_map{height: 400px;width:100%}

#r-result{position:absolute;}


.choose-city-div{z-index:2222;position:fixed;background:#fff;border-radius:4px;display:none;width:650px;min-height:350px;top:50%;left:50%;height:auto;margin-left: -325px; margin-top: -175px;}
.choose-city-overlay{width:100%;height:100%;background:#000;opacity:.5;z-index:2221;position:fixed;display:none;top:0}
.choose-city-div .city-content{padding:10px 20px;font-size:16px}
.choose-city-div .header{border-bottom:1px solid #aaa;padding:10px 0}
.choose-city-div .close_div{position:absolute;right:20px;top:18px;font-size:18px;cursor:pointer}
.choose-city-div .guess-position{padding:20px 0;float:left;width:20%;}
.choose-city-div .content-bottom .position-li, .choose-city-div .content-position .position .position-li{display:inline-block;float:left;margin:0 10px 10px 0;padding:5px 15px;cursor:pointer}
.choose-city-div .content-bottom .position-li.active{background-color:#47aa4d;color:#fff;border-radius:40px;cursor:pointer}
.choose-city-div .content-position {overflow:hidden;}
.choose-city-div .content-position .position{float:left;width:65%;padding: 20px 0 0;cursor:pointer}
.choose-city-div .content-position .position .position-li.active{background-color:#47aa4d;color:#fff;border-radius:40px;cursor:pointer}
.choose-city-div .content-bottom .title{width:20%;float:left;padding-top: 3px;}
.choose-city-div .content-bottom .position-list{width:80%;float:left;max-height: 200px;overflow: auto;}
